/*!
 * Egypt ETA PDF Tool, Chrome Extension Build.
 * © 2023-2025 <PERSON>. All Rights Reserved.
 * This code is protected by copyright law.
 * Unauthorized reproduction or distribution may result in legal action.
 * Licensed for use only with valid subscription from extension.abouelela.net
 * Generated: 2025-08-20T18:53:13.678Z
 */
const e="34";var t=location.href,o=[],i=[],n=[],a=0;const r=300;var d=window.XMLHttpRequest,s="";function c(){var e=new d;return e.addEventListener("readystatechange",function(){if(4==e.readyState&&200==e.status){var t="https://api-portal.invoicing.eta.gov.eg/api/v1/documents",a="https://api-portal.invoicing.eta.gov.eg/api/v1/receipts";if(200==e.status&&(e.responseURL.match(`^${t}/recent`)||e.responseURL.match(`^${t}/search`))&&!p){s=e.responseURL;var r=JSON.parse(e.responseText);u=r.metadata.totalCount,o=r.result}200!=e.status||!e.responseURL.match(`^${a}/recent`)&&!e.responseURL.match(`^${a}/search`)||p||(s=e.responseURL,r=JSON.parse(e.responseText),u=r.metadata.totalCount,i=r.receipts),200==e.status&&e.responseURL.match("^https://api-portal.invoicing.eta.gov.eg/api/v1/codetypes/codes/my")&&(s=e.responseURL,r=JSON.parse(e.responseText),u=r.metadata.totalCount,n=r.result)}},!1),e}window.XMLHttpRequest=c;var l="ar",u=0,p=!1,h=!1;const f="data:image/png;base64,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",b="data:image/png;base64,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***********************************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";function calculateT4Amount(e){let t=0;if(e.invoiceLines&&e.invoiceLines.length>0)for(const o of e.invoiceLines){const e=o.lineTaxableItems?.length>0?o.lineTaxableItems:o.taxableItems;if(e&&e.length>0)for(const o of e)"T4"===o.taxType&&(t+=o.amount||0)}if(e.taxTotals&&e.taxTotals.length>0)for(const o of e.taxTotals)"T4"===o.taxType&&(t+=o.amount||0);return t}function hasT4AtMoreThanOnePercent(e){if(e.invoiceLines&&e.invoiceLines.length>0)for(const t of e.invoiceLines){const e=t.lineTaxableItems?.length>0?t.lineTaxableItems:t.taxableItems;if(e&&e.length>0)for(const t of e)if("T4"===t.taxType&&t.rate>1)return!0}return!1}function createEnhancedModal(){var e=$('<div class="modal fade" id="downloadModal" data-backdrop="static" tabindex="-1"></div>'),t=$('<div class="modal-dialog" style="width:780px;max-width:95vw;direction:ltr;"></div>'),o=$('<div class="modal-content"></div>'),i=ee(),n=$(`<p style="font-size:16px;display:none;" class="subscriptionUrl"><a href="https://extension.abouelela.net/?email=${i}" target="_blank">إضغط هنا لتفعيل الإشتراك</a></p>`),a=$('<p style="font-size:15px;direction:rtl;">هذا العمل فردى وليس له علاقة بأى جهة غير المطور لمزيد من المعلومات يرجى زيارة: <a href="https://abouelela.net" target="_blank">abouelela.net</a></p>'),r=$('<h5 class="modal-title fs-5 bold text-center w-100"></h5>').append(n).append(a),d=$('<button type="button" class="close" id="modalCloseX" aria-label="Close" style="position:absolute;right:15px;top:15px;font-size:20px;"><span aria-hidden="true">&times;</span></button>'),s=$('<div class="modal-header pb-0" style="position:relative;"></div>').append(r).append(d),c=$('<div id="progressContainer" style="margin:15px;display:none;"></div>'),l=$('<div class="progress" style="height:15px;border-radius:4px;background-color:#e0e0e0;overflow:hidden;"></div>'),u=$('<div id="progressBar" class="progress-bar" role="progressbar" style="height:100%;background-color:#2196F3;width:0%;transition:width 0.3s ease;text-align:center;color:white;font-size:12px;line-height:15px;"></div>'),p=$('<div id="progressText" style="text-align:center;font-size:14px;margin-top:5px;color:#424242;"></div>'),f=$('<button id="inlineStopBtn" type="button" style="display:block;margin:10px auto 0;padding:5px 15px;background-color:#dc3545;border:1px solid #dc3545;color:white;border-radius:4px;">Stop</button>');l.append(u),c.append(l).append(p).append(f);var b=$('<div class="modal-body" style="padding:15px;"></div>'),m=$('<div id="optionsContainer" style="display:flex;flex-wrap:wrap;margin:-7px;"></div>'),A=$('<div style="flex:0 0 50%;padding:7px;box-sizing:border-box;"></div>'),g=$('<div style="border:1px solid #ddd;border-radius:4px;overflow:hidden;"></div>'),y=$('<div class="bg-primary pdfDownloadOptions" style="color:white;padding:10px;border-bottom:1px solid #ddd;font-weight:bold;text-align: right;"> PDF - خيارات إسم الملف</div>'),D=$('<div style="padding:10px;" class="pdfDownloadOptions"></div>'),T=$('<div style="display:flex;flex-wrap:wrap;margin:-5px;"></div>'),C=$('<div style="flex:0 0 50%;padding:5px;box-sizing:border-box;"></div>'),I=$('<div style="flex:0 0 50%;padding:5px;box-sizing:border-box;"></div>');C.append($('<div class="form-check text-right"></div>').append($('<input class="form-check-input" type="checkbox" id="option-seller-name">'),$('<label class="form-check-label" for="option-seller-name">إسم البائع</label>')),$('<div class="form-check text-right"></div>').append($('<input class="form-check-input" type="checkbox" id="option-seller-id">'),$('<label class="form-check-label" for="option-seller-id">الرقم الضريبى للبائع</label>')),$('<div class="form-check text-right"></div>').append($('<input class="form-check-input" type="checkbox" id="option-buyer-name">'),$('<label class="form-check-label" for="option-buyer-name">إسم المشترى</label>')),$('<div class="form-check text-right"></div>').append($('<input class="form-check-input" type="checkbox" id="option-buyer-id">'),$('<label class="form-check-label" for="option-buyer-id">الرقم الضريبى للمشترى</label>')),$('<div class="form-check text-right"></div>').append($('<input class="form-check-input" type="checkbox" id="option-po-number">'),$('<label class="form-check-label" for="option-type">مرجع طلب الشراء</label>')),$('<div class="form-check text-right"></div>').append($('<input class="form-check-input" type="checkbox" id="option-so-number">'),$('<label class="form-check-label" for="option-type">مرجع طلب البيع</label>'))),I.append($('<div class="form-check text-right"></div>').append($('<input class="form-check-input" type="checkbox" checked id="option-id">'),$('<label class="form-check-label" for="option-id">الرقم الداخلى</label>')),$('<div class="form-check text-right"></div>').append($('<input class="form-check-input" type="checkbox" checked id="option-uuid">'),$('<label class="form-check-label" for="option-uuid">الرقم الإلكترونى</label>')),$('<div class="form-check text-right"></div>').append($('<input class="form-check-input" type="checkbox" id="option-date">'),$('<label class="form-check-label" for="option-date">تاريخ الفاتورة</label>')),$('<div class="form-check text-right"></div>').append($('<input class="form-check-input" type="checkbox" id="option-type">'),$('<label class="form-check-label" for="option-type">نوع المستند</label>')),$('<div class="form-check text-right"></div>').append($('<input class="form-check-input" type="checkbox" id="option-separate-seller">'),$('<label class="form-check-label" for="option-separate-seller">مجلد لكل بائع</label>')),$('<div class="form-check text-right"></div>').append($('<input class="form-check-input" type="checkbox" id="option-separate-buyer">'),$('<label class="form-check-label" for="option-separate-buyer">مجلد لكل مشترى</label>'))),g.append(y).append(D),A.append(T),T.append(C).append(I),D.append(T),A.append(g);var E=$('<div style="flex:0 0 50%;margin-top:10px;box-sizing:border-box;"></div>'),N=$('<div style="border:1px solid #ddd;border-radius:4px;overflow:hidden;padding-bottom: 3px;"></div>'),M=$('<div style="background-color:#f8f9fa;padding:10px;border-bottom:1px solid #ddd;font-weight:bold;text-align: right;">خيارات التحميل</div>'),B=$('<div style="padding:10px;"></div>'),P=$('<div class="form-check text-right"></div>'),F=$('<span id="modalTotalCountText"></span>'),R=$('<span id="docProfileText"></span>'),S=$('<input class="form-check-input" type="checkbox" checked id="option-download-all">'),O=$('<label class="form-check-label bold" for="option-download-all">تحميل جميع الصفحات - </label>').append(F).append(" ").append(R);P.append(S).append(O);var Q=$('<div class="form-check text-right receiptDownloadOption"></div>').append($('<input class="form-check-input" type="checkbox" id="option-download-details">'),$('<label class="form-check-label bold" for="option-download-details">تحميل بيانات الإيصال - وقت تحميل أطول</label>')),Y=$('<div class="form-check text-right receiptDownloadOption"></div>').append($('<input class="form-check-input" type="checkbox"  id="option-receipt-sort">'),$('<label class="form-check-label bold" style="color:red;" for="option-receipt-sort">حدد هذا الإختيار وإعادة المحاولة فى حالة فشل التحميل أكثر من 3000 إيصال</label>'));B.append(P).append(Q).append(Y),N.append(M).append(B),E.append(N);var z=$('<div style="flex:0 0 50%;padding:7px;box-sizing:border-box;"></div>'),G=$('<div style="border:1px solid #ddd;border-radius:4px;overflow:hidden;"></div>'),V=$('<div class="bg-success" style="color:white;padding:10px;border-bottom:1px solid #ddd;font-weight:bold;text-align: right;">أعمدة ملف الإكسيل</div>'),j=$('<div style="padding:10px;"></div>'),L=$('<div style="display:flex;flex-wrap:wrap;margin:-5px;"></div>'),H=$('<div style="flex:0 0 50%;padding:5px;box-sizing:border-box;"></div>'),W=$('<div style="flex:0 0 50%;padding:5px;box-sizing:border-box;"></div>');H.append($('<div class="form-check text-right"></div>').append($('<input class="form-check-input column-option" type="checkbox" checked id="col-type">'),$('<label class="form-check-label" for="col-type">نوع المستند</label>')),$('<div class="form-check text-right"></div>').append($('<input class="form-check-input column-option" type="checkbox" id="col-version">'),$('<label class="form-check-label" for="col-version">نسخة المستند</label>')),$('<div class="form-check text-right"></div>').append($('<input class="form-check-input column-option" type="checkbox" checked id="col-status">'),$('<label class="form-check-label" for="col-status">الحالة</label>')),$('<div class="form-check text-right"></div>').append($('<input class="form-check-input column-option" type="checkbox" checked id="col-dateIssued">'),$('<label class="form-check-label" for="col-dateIssued">تاريخ الإصدار</label>')),$('<div class="form-check text-right"></div>').append($('<input class="form-check-input column-option" type="checkbox" checked id="col-dateReceived">'),$('<label class="form-check-label" for="col-dateReceived">تاريخ الإستلام</label>')),$('<div class="form-check text-right"></div>').append($('<input class="form-check-input column-option" type="checkbox" checked id="col-issuerDetails">'),$('<label class="form-check-label" for="col-issuerDetails">بيانات البائع</label>')),$('<div class="form-check text-right"></div>').append($('<input class="form-check-input column-option" type="checkbox" checked id="col-buyerDetails">'),$('<label class="form-check-label" for="col-buyerDetails">بيانات المشترى</label>'))),W.append($('<div class="form-check text-right"></div>').append($('<input class="form-check-input column-option" type="checkbox" checked id="col-uuid">'),$('<label class="form-check-label" for="col-uuid">الرقم الإلكترونى</label>')),$('<div class="form-check text-right"></div>').append($('<input class="form-check-input column-option" type="checkbox" id="col-signature">'),$('<label class="form-check-label" for="col-signature">التوقيع الإلكترونى</label>')),$('<div class="form-check text-right"></div>').append($('<input class="form-check-input column-option" type="checkbox" id="col-url">'),$('<label class="form-check-label" for="col-url">الرابط الخارجى</label>')),$('<div class="form-check text-right"></div>').append($('<input class="form-check-input column-option" type="checkbox" id="col-po">'),$('<label class="form-check-label" for="col-po">مرجع طلب الشراء</label>')),$('<div class="form-check text-right"></div>').append($('<input class="form-check-input column-option" type="checkbox" id="col-so">'),$('<label class="form-check-label" for="col-so">مرجع طلب البيع</label>')),$('<div class="form-check text-right"></div>').append($('<input class="form-check-input column-option" type="checkbox" checked id="col-tax-columns">'),$('<label class="form-check-label" for="col-tax-columns">أعمدة الضريبة</label>')),$('<div class="form-check text-right"></div>').append($('<input class="form-check-input column-option" type="checkbox" checked id="col-discount-columns">'),$('<label class="form-check-label" for="col-discount-columns">أعمدة الخصومات</label>')));var U=$("<div></div>");U.append($('<div class="form-check text-right"></div>').append($('<input class="form-check-input" type="checkbox" checked id="option-combine-all">'),$('<label class="form-check-label bold" for="option-combine-all">تجميع الأصناف فى صفحة واحدة</label>')),$('<div class="form-check text-right"></div>').append($('<input class="form-check-input" type="checkbox" checked id="option-negative-credit">'),$('<label class="form-check-label bold" for="option-negative-credit">تحويل مبلغ إشعار الخصم إلى رقم سالب</label>')),$('<div class="form-check text-right"></div>').append($('<input class="form-check-input" type="checkbox" id="option-valid-only">'),$('<label class="form-check-label bold" for="option-valid-only">تجاهل الفواتير المرفوضة والملغية (الغير صحيحة)</label>'))),L.append(W).append(H),j.append(L).append(U),G.append(V).append(j),z.append(G),A.append(E),m.append(A).append(z),b.append(m).append(c),i=ee();var Z=$(`<div class="modal-footer subscriptionDiv" style="align-self:center;display:none;"><p style="font-size:16px;text-align:center;"><a style="color:red;" href="https://extension.abouelela.net/?email=${i}" target="_blank">إنتهت صلاحية الإشتراك<br/>إضغط هنا للتجديد</a></p></div>`),X=$('<div id="downloadModalFooter" class="modal-footer" style="display:flex;justify-content:space-between;padding:10px 15px;border-top:1px solid #ddd;"></div>'),K=$("<div></div>"),q=$('<button type="button" style="margin:0;padding:6px 12px;background-color:#dc3545;border:1px solid #dc3545;color:white;border-radius:4px;" id="closeBtn" class="btn btn-danger">Close</button>');K.append(q);var J=$('<div style="display:flex;gap:5px;"></div>'),_=w("EXCEL","reportsBtn",[{text:"تقرير شامل",disabled:!1,action:function(){excelDownloadButtonClicked()}},{text:"مبيعات - قيمة مضافة - مجمع",className:"pdfDownloadOptions",disabled:!1,action:function(){x(!1)}},{text:"مبيعات - قيمة مضافة - تفصيلى",className:"pdfDownloadOptions",disabled:!1,action:function(){x(!0)}},{text:"مشتريات - مجمع",className:"pdfDownloadOptions",disabled:!1,action:function(){v(!1)}},{text:"مشتريات - تفصيلى",className:"pdfDownloadOptions",disabled:!1,action:function(){v(!0)}},{text:"نموذج 41",className:"pdfDownloadOptions",disabled:!1,action:function(){k(!0)}}]),te=$('<button type="button" style="margin:0;padding:6px 12px;background-color:#007bff;border:1px solid #007bff;color:white;border-radius:4px;display:flex;align-items:center;justify-content:center;gap:5px;" id="submitDownloadInvoices" class="btn btn-primary"><img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAAAXNSR0IArs4c6QAABZJJREFUaEPtmXtQVGUYxp+9nLN7dtmzF1wwWEWMBZOrCJlIM4U0TYPVjKClo5OmQ4CahYqmYF6TvBGZBZU5NY1/lOZYpjB4AVPBUBnFy4jIJZCAZRGwBXa5bHNOI4kLxh7gbM7w/Xu+y/N73+979t3vE+AJb4InXD9GABydwZEM/G8zUPCKF93d2b5PYEWMI0VaBTgoFEsXPXe8tKUvHf1uofMv6X50tPgHghmIsJzqWXYB5EfqrFwiL5DKQM5YgE7aGR2nDkFYcYPLNDZjpp6o7jPY/WaAK4AiJg6yyFnI//gDPBOzAE2p8aBEg/cK3gBU7+0ERnvg6KJXEbjwXVQd+AwTaXLQWeANQJm0B50Ujeylb0A1To+mitvwU5JQkcJBQfAHsCIN3SoXHI+L7hFMiQWYrJY8GQB0/CZQgdNwKHoaVJ7eaCovYYV7OonhTok5Q/CWAcXsJZBFzERWfDS0AaGoOPELK1okBELUEhBCbgeaNwAqPAr0vET8nr4RbsFhuPDJhp6oj6ZE8HIiOGWBNwBC5wVNciZaqsrQcPMqijJ39BI8SU1CLrb/QPMGwKjVpv0MISXH1f3puH30h14ASlIIf6X9tsorgHLROkhDI1CYvgl/nMm22TITaAKjJCK7thKvANT0aNCzElB35QLObkq0ESoR/WOr9pxnXgHotRmgxupZ4afXxKLx9nUbiLFyMcbKBm6rvAGQ3oFQJ+5Gm7EelLMLmu7cwsmkt20AmOiHaCQgB5gG3gDoZamgfEORvexNTN++D2JKjot7tqIy95gNhFYqgo9iYLbKCwAxRg/NugzUXi7Aua0roJ8xGwELl6PLYkFuchybjUdboIqEgvhvW+UFQJWwBZKAqchPXYOawt9YreEpaXANehbmv5pxOmkxTHU1vRjkYgEmDaBOGnYAYpwPNGs+R2tDLY6/828hJ5JSiEj9EvSY8TDV1yJ3bSza7xl7QegVBFylj7fVYQdQxm2ENCgcxd/tRcmRA70EMoc5fP1u0DpPFuLCrmTcK73Z04fZQaEa6WNtdVgBxO6ecE75mo1sVkIMu+cfbcxhDkv6CNqAEPZTWfZhCMUEpOpREEklIARgCz3GlKyNBlgKT8J8vbBnmmEFUC3ZDIl/GC5nbEd5zpFe2hU6D2h9J0PrHwwXv2CQCqUNXGPJNXR1dsBsbEBLTRX7nVKpoXjKHe2Z60F0dWDYAAjPCdCs3ot2Yx1+jZ0JUkHDxT8ErsFT4RoYim6LBS3V5WiuvIOWqgrcrypHW0M99K/Pgc/M+azYzjYTKvOycLcgD4biSz2AIlICNxdneJgbhw9AvepTkE/7sn9c2owG1mWaym6huaqsT9t8OPyURguPiCiMeT6SPR8PGmO3xpJi0O7jcG7bKvjKgJfzaob+VkISHgXVvES01tey24epfbg2uasbtP6ToR7vDZnWDaa6atzNz4XhehEYq024ZBg6ACJwGsoqKhG04QuIKRnOp67Gn4VnuWof0LhVVxoGD0B4TgT12kIUfpMOr9mL4Rb6fL8V54BU2dFpUABCuQKq2A/RJVPi1JaVcNZPxJSVW9hf15zl82BuarRDCreunAFEWjeol26DudWEnJRlIOROiNz1LUgnBVvvMHUPH40TgICUYtTG/ejssiIr8S10mO7jxW2Z0Hj7ofTYQVzZl8aHdnYNTgBMacCUCLnJ8TDevIopKzZDFxYBw40inElZypt4zgAinyBQc97Hte8z4Dc/AYzVlWUdRtFXO3kVzxmAGdg8JQq6GXNhqr2L0qyfUFOQy7t4TgAPHjjM3VZcbDTDyum1YGhYLd3WvHXFxhf6mq3fe76Hn5gqTB2obu0aGjV2zmIV4LTBIpi744ah1i4AO9dxWHduN60Ok2u78AiAo5MxkgFHZ+BvrBQsT5dhh+cAAAAASUVORK5CYII=" style="width:24px;height:24px;" alt="PDF icon" /><span>PDF</span></button>'),oe=$('<button type="button" style="margin:0;padding:6px 12px;background-color:#6c757d;border:1px solid #6c757d;color:white;border-radius:4px;display:flex;align-items:center;justify-content:center;gap:5px;" id="jsonXmlBtn" class="btn btn-secondary"><img src="data:image/png;base64,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" style="width:24px;height:24px;" alt="JSON icon" /><span>JSON/XML</span></button>'),ie=$('<button type="button" style="margin:0;padding:6px 12px;background-color:#6C75EA;border:1px solid #6C75EA;color:white;border-radius:4px;display:flex;align-items:center;justify-content:center;gap:5px;" id="btnDownloadTable" class="btn btn-info"><img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAbUlEQVR4nO2WQQqAQAhF3xnqsG1dVHSnth0qVxOFzCohZIIIhQfCB9/ChQLMgAKlMQpMWNNxX2IQyHtgx2yRAU/ya3YKJAWSggIMDqsRzatgcdiMaP6jHXglKSAFfEGgdqBbC+rRP1+Lt96W8QAgU5nAR4vaXwAAAABJRU5ErkJggg==" style="width:24px;height:24px;" alt="Table icon" /><span>Table</span></button>'),ne=$('<button type="button" style="margin:0;padding:6px 12px;background-color:#ffc107;border:1px solid #ffc107;color:black;border-radius:4px;display:flex;align-items:center;justify-content:center;gap:5px;" id="databaseBtn" class="btn btn-secondary"><img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADIAAAAyCAYAAAAeP4ixAAAACXBIWXMAAAsTAAALEwEAmpwYAAACfklEQVR4nO2ay2oUQRiFP0FBN3FmXCdmFePlATS6EMnGB9DoC0SD8waarREJqDt1IS69RAiJCyEZg0l8BsGVuFBDhsHgJlHMSMFpKJohUzVTfYnUgVpM91//pbuqzqmphv5xErgOTAPPgXXgM/AVaAF/1Fq6Zu6tyXZafUcpAMeAKeA18ANoB2rG1yvgpmJkhsvAG2AnlcAGMA/cByaBS3rCx4EqcEitqmujsplUn3n5sH3uKJaJGQznNFySIH+BJT25U4FiHJCvKWBZMZJ468qhZxwBngK7ctgE7gCDZI9BzZ+mYpscHgEHfR0NWG9hG7gLHCV/VIAZazi/U27Or/mtNf7PUzwuWPNoUTl2xTV1+AWMUB6MKCeT21WXDisyNhOvbLil3BouxlsyNuOzbKgqt58uxu0SF1Kz8uuKxNC8xrKh3kshZZ7sbZcOtlTYBC5SPMY66LquSAyXLd0zU9CcqQD3LEJc6qUQIwceWr+bkg1D2efPUEqimPZAOXkXksAw+8eUaGxoMTjtyrJdYHyckc/3HUTj2B75OReSwEjqOWmv9DxaAGaliMelZIc7yPhhFT8u21n13Uz53FasTjK+70LstfwG8BL4HnBj9Q14oX1KrY/8/A2FE9Jnt4FnwCrwCfii7e1vtZaumXsfZGv6THgu823fQiKzZ4h6ZHYiswdBJTI7kdmJzE5k9r0RmT0H1COzE5k9CCqR2YnMTmR2MmL2LU9CzJPZaz7/xq/sA0JsuBhP7ANCvOK6giyqw4bO78p0hrjgs1IO6DOLshHiqs+prn3O/iR1zl4UIe4Cj4HDob98yJMQ14CzBERZCDEYiibEzJD3Vpf/Gv8ACfkqAAqn4L0AAAAASUVORK5CYII=" style="width:24px;height:24px;" alt="Database icon" /><span>قاعدة البيانات</span></button>');return J.append(ne).append(ie).append(oe).append(te).append(_),X.append(K).append(J),o.append(s).append(b).append(Z).append(X),t.append(o),e.append(t),$("#inlineStopBtn").on("click",function(){$("#inlineStopBtn").hide(),h=!0,$("#progressText").text("Stopping Download, Please Wait...")}),e}function m(){$("#modalCloseX").on("click",function(){setTimeout(()=>{finishDownload()},1500)}),$("#submitDownloadInvoices").on("click",function(){if(p)return;const e=D();let t=window.EXTENSION_SUBSCRIPTION;t&&t.isSubscribed?(p=!0,h=!1,g(!0),$("#progressContainer").show(),$("#progressBar").css("width","0%"),$("#progressText").text("Starting Download Process..."),e.downloadAll?Z().then(t=>{downloadAllInvoices(t,"pdf",e)}):downloadAllInvoices(o,"pdf",e)):$(".subscriptionDiv").show()}),atob("************************************************"),window.__etaAuthor="<NAME_EMAIL> 01*********",$("#databaseBtn").on("click",function(){let e=window.EXTENSION_SUBSCRIPTION;e&&e.isSubscribed?I():$(".subscriptionDiv").show()}),$("#jsonXmlBtn").on("click",function(){if(p)return;const e=D();let t=window.EXTENSION_SUBSCRIPTION;t&&t.isSubscribed?(p=!0,h=!1,g(!0),$("#progressContainer").show(),$("#progressBar").css("width","0%"),$("#progressText").text("Starting Download Process..."),"https://invoicing.eta.gov.eg/documents"===window.location.href?e.downloadAll?Z().then(t=>{downloadAllInvoices(t,"data",e)}):downloadAllInvoices(o,"data",e):"https://invoicing.eta.gov.eg/receipts"===window.location.href&&(e.downloadAll?X().then(t=>{downloadJsonForReceipts(t,e)}):downloadJsonForReceipts(i,e))):$(".subscriptionDiv").show()}),$("#btnDownloadTable").on("click",function(){if(p)return;const e=D();let t=window.EXTENSION_SUBSCRIPTION;t&&t.isSubscribed?(p=!0,h=!1,g(!0),$("#progressContainer").show(),$("#progressBar").css("width","0%"),$("#progressText").text("Starting Download Process..."),"https://invoicing.eta.gov.eg/documents"===window.location.href?e.downloadAll?Z().then(t=>{downloadAllInvoices(t,"html",e)}):downloadAllInvoices(o,"html",e):"https://invoicing.eta.gov.eg/receipts"===window.location.href&&(e.downloadAll?X().then(t=>{downloadJsonForReceipts(t,e)}):downloadJsonForReceipts(i,e))):$(".subscriptionDiv").show()}),$("#closeBtn").on("click",function(){$(this).blur(),setTimeout(()=>{finishDownload()},1500)}),$("#option-separate-seller").on("change",function(){$(this).is(":checked")&&$("#option-separate-buyer").prop("checked",!1)}),$("#option-separate-buyer").on("change",function(){$(this).is(":checked")&&$("#option-separate-seller").prop("checked",!1)}),$("input[type='checkbox']").on("change",function(){D()}),A()}function A(){$("#modalCloseX, #closeBtn").attr("data-dismiss","modal"),$("#modalCloseX, #closeBtn").off("click").on("click",function(){$("#downloadModal").modal("hide"),h=!1,p=!1,$(".downloadAllBtn").removeClass("disabled")})}function g(e){e?($("#optionsContainer").hide(),$("#downloadModalFooter").hide(),$("#modalCloseX").hide(),$("#progressContainer").show(),$("#inlineStopBtn").show()):($("#optionsContainer").show(),$("#downloadModalFooter").show(),$("#modalCloseX").show(),$("#progressContainer").hide())}function w(e,t,o,i){var n=(i=i||{}).disabled||!1,a=$('<div style="position:relative;display:inline-block;margin:0 5px;"></div>'),r=$('<button type="button" id="'+t+'" style="margin:0;padding:6px 12px;background-color:'+(n?"#6c757d":"#218838")+";border:1px solid "+(n?"#6c757d":"#218838")+";color:white;border-radius:4px;display:flex;align-items:center;justify-content:center;gap:5px;cursor:"+(n?"not-allowed":"pointer")+';min-width:120px;;"'+(n?" disabled":"")+'><img src="'+f+'" style="width:24px;height:24px;margin-right: 10px;" /><span>'+e+'</span><span style="margin-left:8px;font-size:12px;">▼</span></button>'),d=$('<div style="display:none;position:absolute;top:calc(100% + 2px);left:0;min-width:210px;background:white;border:1px solid #dee2e6;border-radius:4px;box-shadow:0 4px 12px rgba(0,0,0,0.15);z-index:9999;"></div>');return o.forEach(function(e,t){var o=e.disabled||!1,i=e.className||"",n=$('<button type="button" class="'+i+'" title="'+(e.tooltip||"")+'" style="width:100%;padding:10px 15px;background:white;border:none;display:flex;align-items:center;gap:10px;text-align:right;font-size:14px;transition:all 0.2s;'+(t>0?"border-top:1px solid #f0f0f0;":"")+(o?"cursor:not-allowed;":"cursor:pointer;")+'"><img src="'+f+'" style="width:18px;height:18px;" /><span style="color:'+(o?"#999":"#333")+';">'+e.text+"</span></button>");o?n.on("click",function(e){e.preventDefault(),e.stopPropagation()}):(n.hover(function(){$(this).css({"background-color":"#218838","color":"white"}),$(this).find("img").css("opacity","1"),$(this).find("span:first").css("color","white")},function(){$(this).css({"background-color":"white","color":"#333"}),$(this).find("img").css("opacity","1"),$(this).find("span:first").css("color","#333")}),n.on("click",function(t){t.stopPropagation(),d.fadeOut(200),"function"==typeof e.action&&e.action()})),d.append(n)}),n||r.on("click",function(e){e.stopPropagation(),$(".custom-dropdown-menu").not(d).fadeOut(200),d.fadeToggle(200)}),d.addClass("custom-dropdown-menu"),a.append(r),a.append(d),a.setDisabled=function(e){n=e,r.prop("disabled",e),r.css({"background-color":e?"#6c757d":"#218838","border-color":e?"#6c757d":"#218838","cursor":e?"not-allowed":"pointer"}),e&&d.fadeOut(200)},a}function y(e){var t,o,i=!1;t=setInterval(function(){"undefined"!=typeof $&&$.fn&&(i=!0,clearInterval(t),clearTimeout(o),e())},100),o=setTimeout(function(){clearInterval(t),i||console.log("jQuery failed to load after 10 seconds")},1e4)}function excelDownloadButtonClicked(){if(p)return;const e=D();let t=window.EXTENSION_SUBSCRIPTION;t&&t.isSubscribed?(p=!0,h=!1,g(!0),$("#progressContainer").show(),$("#progressBar").css("width","0%"),$("#progressText").text("Starting Download Process..."),window.location.href.startsWith("https://invoicing.eta.gov.eg/documents")?e.downloadAll?Z().then(t=>{downloadExcelSummary(t,e)}):downloadExcelSummary(o,e):window.location.href.startsWith("https://invoicing.eta.gov.eg/receipts")&&(e.downloadAll?X(e.receiptSort).then(t=>{e.downloadDetails?downloadExcelForReceipts(t,e):U(t,e)}):e.downloadDetails?downloadExcelForReceipts(i,e):U(i,e))):$(".subscriptionDiv").show()}function x(e=!1){if(window.location.href.startsWith("https://invoicing.eta.gov.eg/receipts"))return;if(p)return;const t=D();let i=window.EXTENSION_SUBSCRIPTION;i&&i.isSubscribed?s.indexOf("Direction=sent")>0?(p=!0,h=!1,g(!0),$("#progressContainer").show(),$("#progressBar").css("width","0%"),$("#progressText").text("Starting Download Process..."),t.downloadAll?Z().then(o=>{generateTaxAuthorityExcel(o,{lineByLine:e,negativeCredit:t.negativeCredit})}):generateTaxAuthorityExcel(o,{lineByLine:e,negativeCredit:t.negativeCredit})):alert("برجاء تحديد الوثائق المرسلة فقط، سواء من البحث أو شاشة العرض"):$(".subscriptionDiv").show()}function v(e=!1){if(p)return;const t=D();let i=window.EXTENSION_SUBSCRIPTION;i&&i.isSubscribed?s.indexOf("Direction=received")>0?(p=!0,h=!1,g(!0),$("#progressContainer").show(),$("#progressBar").css("width","0%"),$("#progressText").text("Starting Download Process..."),t.downloadAll?Z().then(o=>{generatePurchasesExcel(o,{lineByLine:e,negativeCredit:t.negativeCredit})}):generatePurchasesExcel(o,{lineByLine:e,negativeCredit:t.negativeCredit})):alert("برجاء تحديد الوثائق المستلمة فقط، سواء من البحث أو شاشة العرض"):$(".subscriptionDiv").show()}function k(){if(p)return;const e=D();let t=window.EXTENSION_SUBSCRIPTION;t&&t.isSubscribed?s.indexOf("Direction=received")>0?(p=!0,h=!1,g(!0),$("#progressContainer").show(),$("#progressBar").css("width","0%"),$("#progressText").text("Starting Download Process..."),e.downloadAll?Z().then(e=>{generateT4Excel(e)}):generateT4Excel(o)):alert("برجاء تحديد الوثائق المستلمة فقط، سواء من البحث أو شاشة العرض"):$(".subscriptionDiv").show()}function D(){const e={date:$("#option-date").is(":checked"),id:$("#option-id").is(":checked"),seller_id:$("#option-seller-id").is(":checked"),seller_name:$("#option-seller-name").is(":checked"),buyer_id:$("#option-buyer-id").is(":checked"),buyer_name:$("#option-buyer-name").is(":checked"),type:$("#option-type").is(":checked"),uuid:$("#option-uuid").is(":checked"),salesOrderNumber:$("#option-so-number").is(":checked"),purchaseOrderNumber:$("#option-po-number").is(":checked"),separate_seller:$("#option-separate-seller").is(":checked"),separate_buyer:$("#option-separate-buyer").is(":checked"),downloadAll:$("#option-download-all").is(":checked"),combineAll:$("#option-combine-all").is(":checked"),negativeCredit:$("#option-negative-credit").is(":checked"),validOnly:$("#option-valid-only").is(":checked"),downloadDetails:$("#option-download-details").is(":checked"),receiptSort:$("#option-receipt-sort").is(":checked"),taxColumns:$("#col-tax-columns").is(":checked"),discountColumns:$("#col-discount-columns").is(":checked"),buyerDetails:$("#col-buyerDetails").is(":checked"),issuerDetails:$("#col-issuerDetails").is(":checked"),so:$("#col-so").is(":checked"),po:$("#col-po").is(":checked"),columns:{type:$("#col-type").is(":checked"),version:$("#col-version").is(":checked"),status:$("#col-status").is(":checked"),dateIssued:$("#col-dateIssued").is(":checked"),dateReceived:$("#col-dateReceived").is(":checked"),uuid:$("#col-uuid").is(":checked"),signature:$("#col-signature").is(":checked"),url:$("#col-url").is(":checked"),purchaseOrderReference:$("#col-po").is(":checked"),purchaseOrderDescription:$("#col-po").is(":checked"),salesOrderReference:$("#col-so").is(":checked"),salesOrderDescription:$("#col-so").is(":checked"),issuerId:$("#col-issuerDetails").is(":checked"),issuerName:$("#col-issuerDetails").is(":checked"),issuerAddress:$("#col-issuerDetails").is(":checked"),receiverId:$("#col-buyerDetails").is(":checked"),receiverName:$("#col-buyerDetails").is(":checked"),receiverMobileNumber:$("#col-buyerDetails").is(":checked"),receiverAddress:$("#col-buyerDetails").is(":checked"),so:$("#col-so").is(":checked"),po:$("#col-po").is(":checked"),buyerDetails:$("#col-buyerDetails").is(":checked"),issuerDetails:$("#col-issuerDetails").is(":checked")}};return e.separate_seller&&e.separate_buyer&&($("#option-separate-buyer").prop("checked",!1),e.separate_buyer=!1),localStorage.setItem("newExtensionOptions",JSON.stringify(e)),e}function T(){const e=localStorage.getItem("newExtensionOptions");if(!e)return;const t=JSON.parse(e);if($("#option-date").prop("checked",t.date||!1),$("#option-id").prop("checked",void 0===t.id||t.id),$("#option-seller-id").prop("checked",t.seller_id||!1),$("#option-seller-name").prop("checked",t.seller_name||!1),$("#option-buyer-id").prop("checked",t.buyer_id||!1),$("#option-buyer-name").prop("checked",t.buyer_name||!1),$("#option-type").prop("checked",t.type||!1),$("#option-uuid").prop("checked",void 0===t.uuid||t.uuid),$("#option-so-number").prop("checked",void 0!==t.salesOrderNumber&&t.salesOrderNumber),$("#option-po-number").prop("checked",void 0!==t.purchaseOrderNumber&&t.purchaseOrderNumber),$("#option-separate-seller").prop("checked",t.separate_seller||!1),$("#option-separate-buyer").prop("checked",t.separate_buyer||!1),$("#option-download-all").prop("checked",t.downloadAll||!1),$("#option-combine-all").prop("checked",t.combineAll||!1),$("#option-valid-only").prop("checked",t.validOnly||!1),$("#option-download-details").prop("checked",t.downloadDetails||!1),$("#option-receipt-sort").prop("checked",t.receiptSort||!1),$("#col-tax-columns").prop("checked",t.taxColumns||!1),$("#col-discount-columns").prop("checked",t.discountColumns||!1),$("#col-issuerDetails").prop("checked",t.issuerDetails||!1),$("#col-buyerDetails").prop("checked",t.buyerDetails||!1),$("#col-so").prop("checked",t.so||!1),$("#col-po").prop("checked",t.po||!1),t.columns)for(const e in t.columns){const o=$("#col-"+e);o.length&&o.prop("checked",t.columns[e])}}function C(){return'\n    <div id="backupRestoreModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.7); z-index: *********;">\n        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 30px; border-radius: 15px; max-width: 600px; width: 90%; max-height: 80vh; overflow-y: auto;">\n           \n            <div id="vatIdDisplay" style="display:none;">\n                <span id="currentVatId" style="font-weight: bold; color: #2c5aa0;">جاري التحميل...</span>\n            </div>\n            \n            <div style="display: flex; margin-bottom: 20px; border-bottom: 2px solid #eee;">\n                <button id="backupTab" class="tab-btn active" onclick="switchTab(\'backup\')" style="flex: 1; padding: 10px; border: none; background: #007bff; color: white; cursor: pointer;">نسخ إحتياطي</button>\n                <button id="restoreTab" class="tab-btn" onclick="switchTab(\'restore\')" style="flex: 1; padding: 10px; border: none; background: #f8f9fa; color: #333; cursor: pointer;">إستعادة البيانات</button>\n                <button id="statsTab" class="tab-btn" onclick="switchTab(\'stats\')" style="flex: 1; padding: 10px; border: none; background: #f8f9fa; color: #333; cursor: pointer;">إحصائيات</button>\n            </div>\n            \n            <div id="backupContent" class="tab-content">\n                <div style="text-align: center; margin-bottom: 20px;">\n                    <p style="color: #666; margin-bottom: 10px;">سيتم إنشاء نسخة احتياطية لجميع الفواتير والإيصالات التى تم تحميلها مسبقاً</p>\n                    <p style="color: #666; margin-bottom: 10px;">وذلك لسهولة إسترجاعها وسرعة تحميلها فى أى وقت وبدون الحاجة لإعادة تحميلها مرة أخرى</p>\n                    <p style="color: #666; margin-bottom: 10px;">وتخفيفاً للضغط على السيرفر الخاص بمصلحة الضرائب</p>\n                    <p style="color: #666; margin-bottom: 10px;">سيتم حفظ المستندات التى مضى عليها أكثر من 14 يوم فقط، تفادياً لتغير حالتها سواء بالرفض أو الإلغاء</p>\n                    <p style="color: #666; margin-bottom: 10px;">علماً بأن ذلك لتوفير الوقت فى تحميل بيانات الفواتير من الأصناف ،الضريبة ،الخصم، إلخ</p>\n                    <p style="color: #666; margin-bottom: 10px;">ورغم ذلك سيكون من الضرورى تحميل جميع الصفحات أولا ولكن فى وقت أقل</p>\n                    <p style="color: #666; margin-bottom: 10px;">لمزيد من المعلومات والإستفسارات يرجى التواصل عن طريق الواتساب 01*********</p>\n                </div>\n                <button onclick="performBackup()" style="padding: 15px; border: none; background: #28a745; color: white; border-radius: 5px; cursor: pointer; font-size: 16px;">تحميل النسخة الاحتياطية</button>\n            </div>\n            \n            <div id="restoreContent" class="tab-content" style="display: none;">\n                <div style="margin-bottom: 20px;">\n                    <label style="display: block; margin-bottom: 10px; font-weight: bold;">اختر ملف النسخة الاحتياطية:</label>\n                    <input type="file" id="backupFile" accept=".json" style="padding: 10px; border: 2px dashed #ddd; border-radius: 5px;">\n                </div>\n                \n                <div style="margin-bottom: 20px;">\n                    <label style="display: block; margin-bottom: 10px; font-weight: bold;">خيارات الاستعادة:</label>\n                    <label style="display: block; margin-bottom: 5px;">\n                        <input type="radio" name="conflictResolution" value="skip" checked> تخطي المستندات الموجودة\n                    </label>\n                    <label style="display: block; margin-bottom: 5px;">\n                        <input type="radio" name="conflictResolution" value="update"> تحديث المستندات الموجودة\n                    </label>\n                    <label style="display: block; margin-bottom: 15px;">\n                        <input type="checkbox" id="clearExisting"> مسح جميع المستندات الموجودة أولاً\n                    </label>\n                </div>\n                \n                <button onclick="performRestore()" style="padding: 15px; border: none; background: #17a2b8; color: white; border-radius: 5px; cursor: pointer; font-size: 16px;">استعادة البيانات</button>\n            </div>\n            \n            <div id="statsContent" class="tab-content" style="display: none;">\n                <div style="text-align: center; margin-bottom: 20px;">\n                    <button onclick="window.loadStats()" style="padding: 12px 30px; border: none; background: #6c757d; color: white; border-radius: 5px; cursor: pointer; font-size: 16px;">تحديث البيانات</button>\n                </div>\n                <div id="statsDisplay" style="background: #f8f9fa; padding: 15px; border-radius: 5px; display:none;">\n                </div>\n                \n            </div>\n            \n            <div id="operationProgress" style="display: none; margin: 20px 0;">\n                <div style="background: #f0f0f0; border-radius: 10px; overflow: hidden; margin-bottom: 10px;">\n                    <div id="operationProgressBar" style="background: #007bff; height: 20px; width: 0%; transition: width 0.3s;"></div>\n                </div>\n                <div id="operationStatus" style="font-size: 14px; color: #666; text-align: center;"></div>\n            </div>\n            \n            <div style="display: flex; gap: 10px; justify-content: flex-end; margin-top: 20px;">\n                <button onclick="closeBackupRestoreModal()" style="padding: 10px 20px; border: 1px solid #ddd; background: white; border-radius: 5px; cursor: pointer;">إغلاق</button>\n            </div>\n        </div>\n    </div>'}function I(){window.showBackupRestoreModal()}async function E(){let e=window.EXTENSION_SUBSCRIPTION;if(void 0!==e){if(!e.isSubscribed)return!1;if(u>0){const e=Math.ceil(u/100);var t=new $.Deferred,o=$(".downloadAllBtn");$(o).addClass("disabled"),p=!0;var i=[];$(".downloadAllBtnText").html(`Loading Page(1/${e})`);for(var n=1;e>=n;n++){var a=replaceUrlParam(s,"Ps",100);a=replaceUrlParam(a,"Pn",n),await L(a).then(o=>{i=$.merge($.merge([],i),o.result),$(".downloadAllBtnText").html(`Loading Page(${n}/${e})`),n==e&&t.resolve(i)})}return t.promise()}}}async function getDocumentDetails(e,t="documents",o=0){try{let o=window.EXTENSION_SUBSCRIPTION;if(void 0===o)return;if(!o.isSubscribed)return!1;var i=`https://api-portal.invoicing.eta.gov.eg/api/v1/${t}/${e}/details?documentLinesLimit=1000`;return await new Promise((e,t)=>{$.ajax({url:i,method:"GET",cache:!1,timeout:1e4,headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(JSON.parse(localStorage.USER_DATA).access_token),"Accept-Language":localStorage.i18nextLng||"ar"},xhr:function(){var e=new XMLHttpRequest;return e.onreadystatechange=function(){2==e.readyState&&200==e.status&&(e.responseType="text")},e},success:function(t){e(t)},error:function(e,o,i){t({message:`Request failed: ${o}, ${i}`,isTimeout:"timeout"===o})}})})}catch(i){return(i.isTimeout?20:3)>++o?(await new Promise(e=>setTimeout(e,1e3)),await getDocumentDetails(e,t,o)):void 0}}async function downloadJsonForReceipts(e,t){try{let u=window.EXTENSION_SUBSCRIPTION;if(void 0===u)return;if(!u.isSubscribed)return!1;var o=e.length;if(o>0){var i=new JSZip,n=$(".downloadAllBtn");$(n).addClass("disabled"),$("#cancelDownloadBtn").show(),h=!1,p=!0;var a=0,r=[];updateProgressBar(0,o,"Preparing Download Process...");const u=t?.batchSize||50;for(let t=0;t<e.length&&!h;t+=u){const n=e.slice(t,Math.min(t+u,o));for(const e of n){if(h)break;const t=e.receipt||e;try{updateProgressBar(a,o,`Downloading File ${a+1}/${o}: ${t.uuid.substring(0,8)}...`);const e=await DownloadFile(t.uuid,!1,"receipts");var d=t.uuid.replaceAll(" ","");$.inArray(d,r)>-1&&(d=`${d}_${a}`),r.push(d);var s=se(e),c="<document>"+ce(e)+"</document>",l=new Blob([c],{type:"application/xml"});i.file(d.concat(".json"),s),i.file(d.concat(".xml"),l),updateProgressBar(++a,o,`Downloaded ${a}/${o} Files Successfully`)}catch(e){console.log(`Error downloading file for ${t.uuid}:`,e),updateProgressBar(++a,o,`Downloaded ${a}/${o} Files Successfully`)}if(h){updateProgressBar(a,o,"Saving Downloaded Files...");break}}if(h)break}a>0?(updateProgressBar(a,o,h?"Finalizing Downloaded Files...":"Initializing ZIP File..."),i.generateAsync({type:"blob"}).then(function(e){updateProgressBar(a,o,h?`Download Cancelled - ${a} files saved`:"Operation Completed Successfully!"),setTimeout(()=>{finishDownload(),saveAs(e,"eReceipts.zip")},1500)})):(updateProgressBar(a,o,""),setTimeout(()=>{finishDownload()},1500))}}catch(e){console.log(e),updateProgressBar(0,0,"Error: "+e.message),setTimeout(()=>{finishDownload()},1500)}}async function downloadAllInvoices(e,t,o){try{let i=window.EXTENSION_SUBSCRIPTION;if(void 0===i)return;if(!i.isSubscribed)return!1;const n=e.length;if(0===n)return;$(".downloadAllBtn").addClass("disabled"),h=!1,p=!0;let a=0,r=0;const d=[],s=new JSZip;updateProgressBar(0,n,"Preparing download...");const c=50,l=10,u=new Map;for(let i=0;n>i&&!h;i+=c){const s=e.slice(i,Math.min(i+c,n));for(let e=0;e<s.length&&!h;e+=l){const i=s.slice(e,e+l).map(async e=>{if(h)return null;const i=e.source||e,a=r+1;try{updateProgressBar(a-1,n,`Processing invoice ${a} of ${n}`);const e="pdf"===t||"data"===t,r="pdf"===t,s=e?await DownloadFile(i.uuid,r):null;if(e&&(!s||0===s.size))return{success:!1,reason:"download-failed"};const c=o.purchaseOrderNumber||o.salesOrderNumber;var d=e&&!c?null:await processInvoice(i.uuid);const l=i.documentTypeNameAr||i.documentTypeNameSecondaryLang,p=i.issueDate||i.dateTimeIssued,h=i.submitterId||i.issuerId||"",f=i.submitterName||i.issuerName||"",b=i.recipientId||i.receiverId||"",m=i.recipientName||i.receiverName||"",A=i.internalId?.replaceAll("\\","-").replaceAll("/","-")||"";let g="";o.date&&(g=ne(g,te(p))),o.id&&(g=ne(g,`(${A})`)),o.seller_id&&o.seller_name?g=ne(g,`(${h} ${f})`):(o.seller_id&&(g=ne(g,h)),o.seller_name&&(g=ne(g,f))),o.buyer_id&&o.buyer_name?g=ne(g,`(${b} ${m})`):(o.buyer_id&&(g=ne(g,b)),o.buyer_name&&(g=ne(g,m))),o.type&&(g=ne(g,`(${l})`)),o.uuid&&(g=ne(g,i.uuid)),(o.purchaseOrderNumber||o.salesOrderNumber)&&(o.purchaseOrderNumber&&d?.purchaseOrderReference&&(g=ne(g,`PO(${d.purchaseOrderReference})`)),o.salesOrderNumber&&d?.salesOrderReference&&(g=ne(g,`SO(${d.salesOrderReference})`))),void 0!==g&&""!==g||(g=i.uuid),g=g.replaceAll("\\","-").replaceAll("/","-");let w="";o.separate_seller&&(w=`${h} ${f}/`),o.separate_buyer&&(w=`${b} ${m}/`);let y=g,x=w+y;if(u.has(x)){let e=u.get(x)+1;u.set(x,e),y=`${g} (${e})`}else u.set(x,1);return{success:!0,fileName:w+y,blob:s,invoiceJsonData:d}}catch(e){return console.log(`Error processing invoice ${i.uuid}:`,e),{success:!1,reason:"processing-error"}}finally{r++}}),c=await Promise.all(i);for(const e of c)e&&e.success&&(a++,d.push(e)),updateProgressBar(r,n,`Processed ${r} of ${n}...`)}}if(a>0){const e=(new Date).toISOString().slice(0,19).replace(/[:.]/g,"-"),o=ue();switch(updateProgressBar(n,n,"Finalizing... creating download file."),t){case"pdf":d.forEach(e=>{s.file(e.fileName+".pdf",e.blob)});const t=await s.generateAsync({type:"blob"});saveAs(t,`eInvoices_PDFs_${o}_${e}.zip`);break;case"data":d.forEach(e=>{var t=se(e.blob),o="<document>"+ce(e.blob).toString().replaceAll("&","&amp;")+"</document>",i=new Blob([o],{type:"application/xml"});s.file(e.fileName.concat(".json"),t),s.file(e.fileName.concat(".xml"),i)});const i=await s.generateAsync({type:"blob"});saveAs(i,`eInvoices_Data_${o}_${e}.zip`);break;case"html":const n=d.map(e=>e.invoiceJsonData),a=N(await pe(),n),r=new Blob([a],{type:"text/html"});saveAs(r,`Extension_Invoice_Package_${o}_${e}.html`)}}updateProgressBar(r,n,`${h?"Download cancelled by user.":"Download complete."} (${a} successful)`),setTimeout(finishDownload,2500)}catch(e){console.log("Download process failed:",e),updateProgressBar(0,0,"Error: "+e.message),setTimeout(finishDownload,2500)}}async function DownloadFile(e,t,o="documents",i=0){let n=window.EXTENSION_SUBSCRIPTION;if(void 0!==n){if(!n.isSubscribed)return!1;var a=t?"pdf":"raw";try{var r,d=`https://api-portal.invoicing.eta.gov.eg/api/v1/${o}/${e}/${a}`;return await $.ajax({url:d,method:"GET",cache:!1,timeout:15e3,headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(JSON.parse(localStorage.USER_DATA).access_token),"Accept-Language":localStorage.i18nextLng||"ar"},xhr:function(){var e=new XMLHttpRequest;return e.onreadystatechange=function(){2==e.readyState&&(200==e.status?e.responseType=t?"blob":"text":e.responseType="text")},e},success:function(e){return r=t?new Blob([e],{type:"application/pdf"}):e}}),atob("************************************************"),window.__etaAuthor="<NAME_EMAIL>",r}catch(n){const a=0===n.status||"timeout"===n.statusText;if((a?25:3)>++i){const n=a?2e3:1e3;return await new Promise(e=>setTimeout(e,n)),await DownloadFile(e,t,o,i)}return void console.log(`Failed to download ${e} after ${i} retries`)}}}function N(t,o){return`<!DOCTYPE html>\n<html><head><title>Extension Invoices</title><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><style>${t.datatablesCSS||""}*{box-sizing:border-box}body{font-family:'Segoe UI',Tahoma,Geneva,Verdana,sans-serif;margin:0;padding:20px;background:linear-gradient(135deg,#667eea 0%,#764ba2 100%);min-height:100vh;color:#333}.container{background:white;border-radius:15px;box-shadow:0 10px 30px rgba(0,0,0,0.3);overflow:hidden;max-width:1400px;margin:0 auto}.header{background:linear-gradient(45deg,#007bff,#0056b3);color:white;padding:10px;text-align:center}.header h1{font-size:28px;margin:0 0 10px 0;font-weight:700}.header .subtitle{font-size:16px;opacity:0.9;margin:0}.header-logo{width:80px;height:80px;margin:0 auto;display:block}.header .version{font-size:14px;opacity:0.8;margin:0}.main-actions{padding:30px;background:#f8f9fa;display:grid;grid-template-columns:1fr 1fr;gap:20px}.btn{padding:15px 25px;border:none;border-radius:10px;font-size:16px;font-weight:600;cursor:pointer;transition:all 0.3s ease;display:flex;align-items:center;justify-content:center;gap:10px}.btn:hover{transform:translateY(-2px);box-shadow:0 5px 15px rgba(0,0,0,0.2)}.merge-btn{background:#007bff;color:white}.merge-btn:hover{background:#0056b3}.browse-styled-btn{background:#17a2b8;color:white}.browse-styled-btn:hover{background:#138496}.download-btn{background:#28a745;color:white}.download-btn:hover{background:#1e7e34}.browse-btn{background:#6f42c1;color:white}.browse-btn:hover{background:#5a32a3}.btn:disabled{background:#6c757d!important;cursor:not-allowed;transform:none!important}.search-section{padding:20px 30px;background:white;border-bottom:1px solid #dee2e6}.search-container{display:flex;gap:15px;margin-bottom:15px}.search-input{flex:1;padding:12px 15px;border:2px solid #dee2e6;border-radius:8px;font-size:14px;transition:border-color 0.3s}.search-input:focus{outline:none;border-color:#007bff}.search-stats{font-size:14px;color:#6c757d;display:flex;justify-content:space-between;align-items:center}.invoice-table-container{padding:0 30px 30px 30px;max-height:620px;overflow-y:auto}.invoice-table{width:100%;border-collapse:collapse;background:white;border-radius:8px;overflow:hidden;box-shadow:0 2px 10px rgba(0,0,0,0.1)}.invoice-table th{background:#f8f9fa;padding:15px 10px;text-align:left;font-weight:600;border-bottom:2px solid #dee2e6;position:sticky;top:0;z-index:10}.invoice-table td{padding:12px 10px;border-bottom:1px solid #eee;vertical-align:middle}.invoice-table tr:hover{background:#f8f9fa}.invoice-table tr.hidden{display:none}.invoice-filename{font-weight:400;color:#333;max-width:250px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.invoice-size{font-size:12px;color:#6c757d}.invoice-index{font-weight:600;color:#007bff;text-align:center;width:60px}.action-btns{display:flex;gap:8px}.action-btn{padding:6px 12px;border:none;border-radius:5px;font-size:12px;font-weight:500;cursor:pointer;transition:all 0.2s;display:flex;align-items:center;gap:4px}.download-individual{background:#17a2b8;color:white}.download-individual:hover{background:#138496}.print-individual{background:#ffc107;color:#212529}.print-individual:hover{background:#e0a800}.view-individual{background:#6c757d;color:white}.view-individual:hover{background:#545b62}.progress-container{margin:20px 0;background:#e9ecef;border-radius:10px;overflow:hidden;height:8px;display:none}.progress-bar{height:100%;background:linear-gradient(45deg,#007bff,#0056b3);width:0%;transition:width 0.3s ease}.status{font-size:14px;color:#6c757d;margin:10px 0;text-align:center;display:none}.no-results{text-align:center;padding:40px;color:#6c757d;font-style:italic;display:none}@media (max-width:768px){.main-actions{grid-template-columns:1fr}.search-container{flex-direction:column}.invoice-table{font-size:12px}.invoice-table th,.invoice-table td{padding:8px 5px}.action-btns{flex-direction:column}}.dataTables_wrapper{margin-top:20px;direction:rtl}.dataTables_length,.dataTables_filter,.dataTables_info,.dataTables_paginate{margin:10px 0}.dataTables_filter input{padding:8px;border:1px solid #dee2e6;border-radius:4px;margin-left:8px}.dataTables_length select{padding:5px;border:1px solid #dee2e6;border-radius:4px;margin:0 8px}.dt-buttons{margin-bottom:15px}.dt-button{background:#007bff!important;color:white!important;border:none;padding:8px 12px;margin:2px;border-radius:4px;cursor:pointer}.dt-button:hover{background:#0056b3!important}table.dataTable{border-collapse:collapse!important}table.dataTable thead th{background:#f8f9fa!important;border-bottom:2px solid #dee2e6;padding:12px 8px;font-weight:600;text-align:center}table.dataTable tbody td{padding:8px;border-bottom:1px solid #eee;vertical-align:middle;text-align:center}table.dataTable tbody tr:hover{background:#f8f9fa!important}.text-right{text-align:right!important}.text-center{text-align:center!important}.text-left{text-align:left!important}</style></head><body><div class="container"><div class="header"><img src="${b}" alt="ETA Tool Logo" class="header-logo"><h1 style="margin:0;">Egypt ETA PDF Tool</h1><div class="subtitle">Chrome Extension</div><div class="version">Version ${e}</div></div><div class="invoice-table-container" id="advancedTableContainer"><table id="invoiceDataTable" class="display nowrap" style="width:100%"><thead></thead><tbody></tbody></table></div></div><script>${t.qrcode||"/* qrcode not loaded */"}${t.datatables||"/* DataTables not loaded */"}<\/script><script>const invoiceJsonData=${JSON.stringify(o)};let allInvoices=[];let currentView='simple';let dataTable = null;function formatFileSize(bytes) {if (bytes === 0) return '0 B';const k = 1024;const sizes = ['B', 'KB', 'MB', 'GB'];const i = Math.floor(Math.log(bytes)/Math.log(k));return parseFloat((bytes / Math.pow(k, i)).toFixed(1))+' '+sizes[i];}function formatNumber(e){return new Intl.NumberFormat("en-US",{minimumFractionDigits:3,maximumFractionDigits:3}).format(e)}function pad(e,t){for(var r=e+"";r.length<t;)r="0"+r;return r}function getFormattedLocalDateTime(e){if(!e)return null;const t=new Date(e),r=pad(t.getDate(),2),n=pad(t.getMonth()+1,2);return t.getFullYear()+"-"+n+"-"+r}function numberToArabicText(e,t){if(!e||0===e)return"";const r=["","واحد","اثنين","ثلاثة","أربعة","خمسة","ستة","سبعة","ثمانية","تسعة"],n=["","","عشرين","ثلاثين","أربعين","خمسين","ستين","سبعين","ثمانين","تسعين"],o=["عشرة","أحد عشر","اثنا عشر","ثلاثة عشر","أربعة عشر","خمسة عشر","ستة عشر","سبعة عشر","ثمانية عشر","تسعة عشر"],a=["","مائة","مائتين","ثلاثمائة","أربعمائة","خمسمائة","ستمائة","سبعمائة","ثمانمائة","تسعمائة"],i={EGP:{major:"جنيه مصري",majorPlural:"جنيه مصري",minor:"قرش",minorPlural:"قرش"},USD:{major:"دولار أمريكي",majorPlural:"دولار أمريكي",minor:"سنت",minorPlural:"سنت"},EUR:{major:"يورو",majorPlural:"يورو",minor:"سنت",minorPlural:"سنت"},GBP:{major:"جنيه إسترليني",majorPlural:"جنيه إسترليني",minor:"بنس",minorPlural:"بنس"}}[t]||{major:t,majorPlural:t,minor:t+" فرعي",minorPlural:t+" فرعي"};function c(e){if(0===e)return"";let t="";const i=Math.floor(e/100),c=Math.floor(e%100/10),u=e%10;return i>0&&(t+=a[i]),1===c?(t&&(t+=" و"),t+=o[u]):u>0&&c>0?(t&&(t+=" و"),t+=r[u]+" و"+n[c]):c>0?(t&&(t+=" و"),t+=n[c]):u>0&&(t&&(t+=" و"),t+=r[u]),t}let u=Math.floor(e);const l=Math.round(100*(e-u));let m="";if(u>=1e6){const e=Math.floor(u/1e6),t=c(e);m+=1===e?"مليون ":2===e?"مليونين ":t+" مليون ",u%=1e6}if(u>=1e3){const e=Math.floor(u/1e3),t=c(e);m&&(m+="و"),m+=1===e?"ألف ":2===e?"ألفين ":e>=3&&e<=10?t+" آلاف ":t+" ألف ",u%=1e3}if(u>0){m&&(m+="و"),m+=c(u)+" "}return m+=i.majorPlural,l>0&&(m+=" و"+c(l)+" "+i.minorPlural),m.trim()+" فقط لا غير"}function getInvoiceVersion(e){return e.documentTypeVersion||"1.0"}function getInvoiceCurrency(e){if("Foreign"===e.currenciesSold){if(e.invoiceLines&&e.invoiceLines.length>0){const t=e.invoiceLines[0];if(t.unitValue&&t.unitValue.currencySold)return t.unitValue.currencySold}return e.currencySegments&&e.currencySegments.length>0?e.currencySegments[0].currency:"USD"}return"EGP"}function getDocumentTypeName(e){const t=e.documentType.toUpperCase();switch(t){case"I":return"فاتورة";case"D":return"إشعار مدين";case"C":return"إشعار دائن";default:return t}}function getInvoiceStatus(e){var t=e.status.toUpperCase();"VALID"===t&&null!=e.cancelRequestDate&&null==e.declineCancelRequestDate&&(t="CANCELLATION REQUESTED"),"VALID"===t&&null!=e.rejectRequestDate&&null==e.declineRejectRequestDate&&(t="REJECTION REQUESTED");return{VALID:{text:"✓ صحيحة",color:"#28a745"},INVALID:{text:"✗ غير صحيحة",color:"#dc3545"},SUBMITTED:{text:"⏳ قيد الإرسال",color:"#ffc107"},CANCELLED:{text:"✗ ملغية",color:"#dc3545"},"CANCELLATION REQUESTED":{text:"✗ تم تقديم طلب إلغاء",color:"#dc3545"},REJECTED:{text:"✗ مرفوضة",color:"#dc3545"},"REJECTION REQUESTED":{text:"✗ تم تقديم طلب رفض",color:"#dc3545"}}[t]||{text:"? "+t,color:"#6c757d"}}function getItemDisplayName(e){const t=e.itemPrimaryName||"";return e.itemSecondaryName||""||t||""}function getItemSecondLine(e){return e.description===getItemDisplayName(e)?"":'<div style="margin-bottom: 1px; font-size: 9px;">'+getItemDisplayName(e)+"</div>"}function generateQRCode(e){const t=qrcode(0,"M");return t.addData(e),t.make(),t.createDataURL(4,6)}document.addEventListener("DOMContentLoaded",(function(){initializeDataTable()}));function generateInvoiceLines(e,t){return e.map((function(e,o){e.lineTaxableItems&&e.lineTaxableItems[0]&&e.lineTaxableItems[0];var r,d,n,i=o%2==0?"#f8f9fa":"white",a=e.internalCode?" / "+e.internalCode:"",l=e.lineTaxableItems?e.lineTaxableItems.map((function(e){return formatNumber(e.amount)})).join("<br>"):formatNumber(0);return"EGP"===t?(r=formatNumber(e.unitValue.amountEGP),d=formatNumber(e.total),n=formatNumber(e.salesTotal)):(r=formatNumber(e.unitValue.amountSold)+" "+t+"<br><span style='font-size: 9px; color: #666;'>("+formatNumber(e.unitValue.amountEGP)+" EGP)</span>",d=formatNumber(e.totalForeign)+" "+t+"<br><span style='font-size: 9px; color: #666;'>("+formatNumber(e.total)+" EGP)</span>",n=formatNumber(e.salesTotalForeign)+" "+t+"<br><span style='font-size: 9px; color: #666;'>("+formatNumber(e.salesTotal)+" EGP)</span>"),'<tr style="background: '+i+';"><td style="padding: 4px 8px; border-right: 1px solid #dee2e6; border-bottom: 1px solid #dee2e6; font-size: 11px; line-height: 1.4;"><div style="font-weight: bold; margin-bottom: 1px;">'+e.description+"</div>"+getItemSecondLine(e)+'<div style="color: #666; font-size: 8px;">'+e.itemCode+a+'</div></td><td style="padding: 4px 8px; text-align: center; border-right: 1px solid #dee2e6; border-bottom: 1px solid #dee2e6; font-size: 11px; line-height: 1.3;"><div>'+formatNumber(e.quantity)+'</div><div style="color: #666; font-size: 10px;">'+e.unitType+'</div></td><td style="padding: 4px 8px; text-align: center; border-right: 1px solid #dee2e6; border-bottom: 1px solid #dee2e6; font-size: 11px;">'+r+'</td><td style="padding: 4px 8px; text-align: center; border-right: 1px solid #dee2e6; border-bottom: 1px solid #dee2e6; font-size: 11px;">'+n+'</td><td style="padding: 4px 8px; text-align: center; border-right: 1px solid #dee2e6; border-bottom: 1px solid #dee2e6; font-size: 11px;">'+formatNumber(0)+'</td><td style="padding: 4px 8px; text-align: center; border-right: 1px solid #dee2e6; border-bottom: 1px solid #dee2e6; font-size: 11px; color: #dc3545; font-weight: bold;">'+l+'</td><td style="padding: 4px 8px; text-align: center; border-bottom: 1px solid #dee2e6; font-size: 11px; font-weight: bold; background: #e3f2fd;">'+d+"</td></tr>"})).join("")}function generateTaxSection(e){if(!e.taxTotals||0===e.taxTotals.length)return"";var t={T1:"ضريبة القيمة المضافة",T2:"ضريبة الجدول (نسبية)",T3:"ضريبة الجدول (قطعية)",T4:"الخصم تحت حساب الضريبة",T5:"ضريبة الدمغة (نسبية)",T6:"ضريبة الدمغة (قطعية بمقدار ثابت)",T7:"ضريبة الملاهى",T8:"رسم تنمية الموارد",T9:"رسم خدمة",T10:"رسم المحليات",T11:"رسم التامين الصحى",T12:"رسوم أخري",T13:"ضريبة الدمغة (نسبية)",T14:"ضريبة الدمغة (قطعيه بمقدار ثابت)",T15:"ضريبة الملاهى",T16:"رسم تنمية الموارد",T17:"رسم خدمة",T18:"رسم المحليات",T19:"رسم التامين الصحى",T20:"رسوم أخرى"},o={V001:"تصدير للخارج",V002:"تصدير مناطق حرة وأخرى",V003:"سلعة أو خدمة معفاة",V004:"سلعة أو خدمة غير خاضعة للضريبة",V005:"إعفاءات دبلوماسين والقنصليات والسفارات",V006:"إعفاءات الدفاع والأمن القومى",V007:"إعفاءات اتفاقيات",V008:"إعفاءات خاصة و أخرى",V009:"سلع عامة",V010:"نسب ضريبة أخرى"};var r=[];return e.invoiceLines&&e.invoiceLines.forEach((function(e){e.lineTaxableItems&&e.lineTaxableItems.forEach((function(e){var t=r.find((function(t){return t.taxType===e.taxType&&t.subType===e.subType}));t?t.amount+=e.amount:r.push({taxType:e.taxType,subType:e.subType||"V009",rate:e.rate||0,amount:e.amount||0})}))})),0===r.length&&e.taxTotals&&e.taxTotals.forEach((function(e){r.push({taxType:e.taxType,subType:"V009",rate:14,amount:e.amount})})),0===r.length?"":'<div class="no-break" style="margin: 10px 0; padding: 6px; background: #fff3cd; border-radius: 8px; border: 1px solid #ffc107;"><table style="width: 100%; font-size: 12px; background: white; border-radius: 5px; overflow: hidden;"><thead><tr style="background: #856404; color: white;"><th style="text-align: center; padding: 4px; font-size: 11px;">نوع الضريبة</th><th style="text-align: center; padding: 4px; border-right: 1px solid #a67c00; font-size: 11px;">النوع الفرعي</th><th style="text-align: center; padding: 4px; border-right: 1px solid #a67c00; font-size: 11px;">نسبة الضريبة</th><th style="text-align: center; padding: 4px; border-right: 1px solid #a67c00; font-size: 11px;">القيمة</th></tr></thead><tbody>'+r.map((function(e){return'<tr><td style="padding: 4px; text-align: center; border-bottom: 1px solid #dee2e6; font-size: 11px;">'+(d=e.taxType,(t[d]||"ضريبة")+'</td><td style="padding: 4px; text-align: center; border-right: 1px solid #dee2e6; border-bottom: 1px solid #dee2e6; font-size: 11px;">')+(r=e.subType,(o[r]||"عام")+'</td><td style="padding: 4px; text-align: center; border-right: 1px solid #dee2e6; border-bottom: 1px solid #dee2e6; font-weight: bold; font-size: 11px;">')+e.rate.toFixed(2)+'%</td><td style="padding: 4px; text-align: center; border-right: 1px solid #dee2e6; border-bottom: 1px solid #dee2e6; color: #dc3545; font-weight: bold; font-size: 11px;">'+formatNumber(e.amount)+"</td></tr>";var r,d})).join("")+"</tbody></table></div>"}function generateTaxTotalsRows(t,e){if(!t.taxTotals||0===t.taxTotals.length)return'<tr><td style="padding: 4px 15px; background: #f8f9fa; border-bottom: 1px solid #dee2e6; font-weight: bold;">الضرائب ('+e+')</td><td style="padding: 4px 15px; text-align: left; background: white; border-bottom: 1px solid #dee2e6; color: #dc3545; font-weight: bold;">'+formatNumber(0)+"</td></tr>";var o={T1:"ضريبة القيمة المضافة",T2:"ضريبة الجدول (نسبية)",T3:"ضريبة الجدول (قطعية)",T4:"الخصم تحت حساب الضريبة",T5:"ضريبة الدمغة (نسبية)",T6:"ضريبة الدمغة (قطعية بمقدار ثابت)",T7:"ضريبة الملاهى",T8:"رسم تنمية الموارد",T9:"رسم خدمة",T10:"رسم المحليات",T11:"رسم التامين الصحى",T12:"رسوم أخري",T13:"ضريبة الدمغة (نسبية)",T14:"ضريبة الدمغة (قطعية بمقدار ثابت)",T15:"ضريبة الملاهى",T16:"رسم تنمية الموارد",T17:"رسم خدمة",T18:"رسم المحليات",T19:"رسم التامين الصحى",T20:"رسوم أخرى"};return t.taxTotals.map((function(i){var r,a=o[i.taxType]||"ضريبة";if("EGP"===e)r=formatNumber(i.amount);else{var n=0;if(t.currencySegments&&t.currencySegments[0]&&t.currencySegments[0].taxTotals){var d=t.currencySegments[0].taxTotals.find((function(t){return t.taxType===i.taxType}));d&&(n=d.amount)}r=formatNumber(n)+" "+e+'<br><span style="font-size: 9px; color: #666;">('+formatNumber(i.amount)+" EGP)</span>"}return'<tr><td style="padding: 4px 15px; background: #f8f9fa; border-bottom: 1px solid #dee2e6; font-weight: bold;">'+a+" ("+e+')</td><td style="padding: 4px 15px; text-align: left; background: white; border-bottom: 1px solid #dee2e6; color: #dc3545; font-weight: bold;">'+r+"</td></tr>"})).join("")}function generateTotalsSection(t,e){var o,i,r,a,n;if("EGP"===e)o=formatNumber(t.totalSales),i=formatNumber(t.totalDiscount),r=formatNumber(t.totalItemsDiscountAmount),a=formatNumber(t.extraDiscountAmount),n=formatNumber(t.totalAmount);else{var d=t.currencySegments&&t.currencySegments[0];d?(o=formatNumber(d.totalSales)+" "+e+'<br><span style="font-size: 10px; color: #666;">('+formatNumber(t.totalSales)+" EGP)</span>",i=formatNumber(d.totalDiscount)+" "+e+'<br><span style="font-size: 10px; color: #666;">('+formatNumber(t.totalDiscount)+" EGP)</span>",r=formatNumber(d.totalItemsDiscountAmount)+" "+e+'<br><span style="font-size: 10px; color: #666;">('+formatNumber(t.totalItemsDiscountAmount)+" EGP)</span>",a=formatNumber(d.extraDiscountAmount)+" "+e+'<br><span style="font-size: 10px; color: #666;">('+formatNumber(t.extraDiscountAmount)+" EGP)</span>",n=formatNumber(d.totalAmount)+" "+e+'<br><span style="font-size: 12px; color: #fff;">('+formatNumber(t.totalAmount)+" EGP)</span>"):(o=formatNumber(t.totalSales),i=formatNumber(t.totalDiscount),r=formatNumber(t.totalItemsDiscountAmount),a=formatNumber(t.extraDiscountAmount),n=formatNumber(t.totalAmount))}return'<tr><td style="padding: 4px 15px; background: #f8f9fa; border-bottom: 1px solid #dee2e6; font-weight: bold;">إجمالي المبيعات ('+e+')</td><td style="padding: 4px 15px; text-align: left; background: white; border-bottom: 1px solid #dee2e6;">'+o+'</td></tr><tr><td style="padding: 4px 15px; background: #f8f9fa; border-bottom: 1px solid #dee2e6; font-weight: bold;">إجمالي الخصم ('+e+')</td><td style="padding: 4px 15px; text-align: left; background: white; border-bottom: 1px solid #dee2e6;">'+i+'</td></tr><tr><td style="padding: 4px 15px; background: #f8f9fa; border-bottom: 1px solid #dee2e6; font-weight: bold;">اجمالي خصم الاصناف ('+e+')</td><td style="padding: 4px 15px; text-align: left; background: white; border-bottom: 1px solid #dee2e6;">'+r+"</td></tr>"+generateTaxTotalsRows(t,e)+'<tr><td style="padding: 4px 15px; background: #f8f9fa; border-bottom: 2px solid #2c5aa0; font-weight: bold;">خصم إضافي على إجمالي الفاتورة ('+e+')</td><td style="padding: 4px 15px; text-align: left; background: white; border-bottom: 2px solid #2c5aa0;">'+a+'</td></tr><tr style="background: linear-gradient(135deg, #2c5aa0, #3d6bb3); color: white;"><td style="padding: 12px 15px; font-weight: bold; font-size: 16px;">المبلغ الإجمالي ('+e+')</td><td style="padding: 12px 15px; text-align: left; font-weight: bold; font-size: 16px;">'+n+'</td></tr><tr style="background: #f8f9fa;"><td colspan="2" style="padding: 8px 15px; text-align: center; font-size: 12px; color: #333; font-style: italic; border-top: 1px solid #dee2e6;">'+numberToArabicText("EGP"===e?t.totalAmount:t.currencySegments&&t.currencySegments[0]?t.currencySegments[0].totalAmount:t.totalAmount,e)+"</td></tr>"}function generateInvoiceHTML(t){var e=generateQRCode(t.publicUrl),o=getInvoiceVersion(t),i=getInvoiceCurrency(t),r=t.purchaseOrderReference?'<div><span style="font-weight: bold;">مرجع طلب الشراء:</span><br><span style="color: #555; font-size: 10px;">'+t.purchaseOrderReference+"</span></div>":"",a=t.salesOrderReference?'<div><span style="font-weight: bold;">مرجع طلب البيع:</span><br><span style="color: #555;">'+t.salesOrderReference+"</span></div>":"";return'<style>@media print{body{margin:0;padding:0}.page-break{page-break-before:always}.no-break{page-break-inside:avoid!important}.avoid-break-before{page-break-before:avoid!important}.avoid-break-after{page-break-after:avoid!important}}.no-break{page-break-inside:avoid!important}.avoid-break-before{page-break-before:avoid!important}.avoid-break-after{page-break-after:avoid!important}table{page-break-inside:avoid!important}.tax-totals-group{page-break-inside:avoid!important}</style><div style="font-family: Segoe UI, Tahoma, Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 15px 15px 30px 15px; background: white; direction: rtl; line-height: 1.4; font-size: 14px;"><div style="border-bottom: 2px solid #2c5aa0; padding-bottom: 8px; margin-bottom: 8px;"><div style="display: flex; justify-content: space-between; align-items: flex-start;"><div style="flex: 1;"><div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 8px; font-size: 11px;"><div><div style="margin-bottom: 2px;"><span style="font-weight: bold;">الرقم الإلكتروني:</span><br><span style="color: #555; font-size: 10px;">'+t.uuid+'</span></div><div style="margin-bottom: 2px;"><span style="font-weight: bold;">الرقم الداخلي:</span><br><span style="color: #555;">'+t.internalID+"</span></div>"+r+'</div><div><div style="margin-bottom: 2px;"><span style="font-weight: bold;">تاريخ التقديم:</span><br><span style="color: #555; font-size: 10px;">'+getFormattedLocalDateTime(t.dateTimeRecevied)+'</span></div><div style="margin-bottom: 2px;"><span style="font-weight: bold;">تاريخ الإصدار:</span><br><span style="color: #555; font-size: 10px;">'+getFormattedLocalDateTime(t.dateTimeIssued)+"</span></div>"+a+'</div><div><div style="margin-bottom: 2px;"><span style="font-weight: bold;">حالة الفاتورة:</span><br><span style="color: '+getInvoiceStatus(t).color+'; font-weight: bold;">'+getInvoiceStatus(t).text+'</span></div><div style="margin-bottom: 2px;"><span style="font-weight: bold;">رقم التسجيل للبائع:</span><br><span style="color: #555;">'+t.issuer.id+'</span></div><div><span style="font-weight: bold;">رقم التسجيل للمشتري:</span><br><span style="color: #555;">'+t.receiver.id+'</span></div></div></div></div><div style="text-align: center; margin-left: 15px;"><div style="display: flex; align-items: center; justify-content: center; margin-bottom:8px"><div style="width: 20px; height: 20px; background: #2c5aa0; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-left: 6px;"><span style="color: white; font-size: 12px;">📄</span></div><h1 style="margin: 0; font-size: 16px; color: #2c5aa0; font-weight: bold;">'+getDocumentTypeName(t)+" v"+o+'</h1></div><img src="'+e+'" style="width: 80px; height: 80px; border: 1px solid #2c5aa0; border-radius: 5px;"></div></div></div><div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; margin-bottom: 8px;"><div style="border: 1px solid #e0e0e0; border-radius: 6px; overflow: hidden;"><div style="background: linear-gradient(135deg, #2c5aa0, #3d6bb3); color: white; padding: 4px; font-weight: bold; text-align: center; font-size: 12px;">البائع</div><div style="padding: 6px; font-size: 11px;"><div style="margin-bottom: 2px;"><span style="color: #666; font-size: 9px;">الاسم</span><br><strong style="font-size: 11px;">'+t.issuer.name+'</strong></div><div><span style="color: #666; font-size: 9px;">رقم التسجيل</span><br><strong>'+t.issuer.id+'</strong></div></div></div><div style="border: 1px solid #e0e0e0; border-radius: 6px; overflow: hidden;"><div style="background: linear-gradient(135deg, #28a745, #34ce57); color: white; padding: 4px; font-weight: bold; text-align: center; font-size: 12px;">المشتري</div><div style="padding: 6px; font-size: 11px;"><div style="margin-bottom: 2px;"><span style="color: #666; font-size: 9px;">الاسم</span><br><strong style="font-size: 11px;">'+t.receiver.name+'</strong></div><div><span style="color: #666; font-size: 9px;">رقم التسجيل</span><br><strong>'+t.receiver.id+'</strong></div></div></div></div><div style="margin-bottom: 20px;"><div style="overflow-x: auto; border: 1px solid #dee2e6; border-radius: 8px 8px 0 0;"><table style="width: 100%; border-collapse: collapse; background: white; font-size: 10px;"><thead><tr style="background: #343a40; color: white;"><th style="padding: 6px 4px; text-align: center; border-right: 1px solid #6c757d; font-size: 11px; font-weight: bold; line-height: 1.2;">البيان</th><th style="padding: 6px 4px; text-align: center; border-right: 1px solid #6c757d; font-size: 11px; font-weight: bold; line-height: 1.2;">الكمية<br>الوحدة</th><th style="padding: 6px 4px; text-align: center; border-right: 1px solid #6c757d; font-size: 11px; font-weight: bold;">سعر الوحدة</th><th style="padding: 6px 4px; text-align: center; border-right: 1px solid #6c757d; font-size: 11px; font-weight: bold;">الإجمالي</th><th style="padding: 6px 4px; text-align: center; border-right: 1px solid #6c757d; font-size: 11px; font-weight: bold;">الخصم</th><th style="padding: 6px 4px; text-align: center; border-right: 1px solid #6c757d; font-size: 11px; font-weight: bold;">الضريبة</th><th style="padding: 6px 4px; text-align: center; font-size: 11px; font-weight: bold;">المجموع</th></tr></thead><tbody>'+generateInvoiceLines(t.invoiceLines,i)+'</tbody></table></div></div><div class="tax-totals-group">'+generateTaxSection(t)+'<div style="display: flex; justify-content: space-between; align-items: flex-start; margin-top: 15px;"><div style="width: 70%;"><div class="no-break" style="border-radius: 8px; overflow: hidden; box-shadow: 0 2px 8px rgba(0,0,0,0.1);"><table style="width: 100%; border-collapse: collapse; font-size: 12px;">'+generateTotalsSection(t,i)+'</table></div></div><div style="width: 30%; text-align: center; padding: 15px; background: linear-gradient(135deg, #f8f9fa, #e9ecef); border-radius: 8px; margin-right: 10px;"><img src="${b}" alt="Logo" style="width: 60px; height: 60px; margin-bottom: 8px;"><div style="font-size: 10px; color: #2c5aa0; font-weight: bold; margin-bottom: 4px;">Egypt ETA PDF Tool</div><div style="font-size: 7px; color: #999;"><a style="color:black;text-decoration:none;" href="https://abouelela.net">abouelela.net</a></div><div style="font-size: 9px; color: #999;">هذا التصميم غير رسمى وإجتهاد شخصي</div><div style="font-size: 9px; color: #999;">إهداء لمصلحة الضرائب المصرية</div></div></div></div>'}        async function generateStyledSingle(e){var t=document.querySelector('button[onclick="generateStyledSingle('+e+')"]'),a=t?t.innerHTML:"🎨";try{t&&(t.disabled=!0,t.innerHTML="⏳");var i=invoiceJsonData[e];if(!i){i.uuid;i=await processInvoice(i.uuid)}if(!i)throw new Error("Could not get invoice data");var n=generateInvoiceHTML(i),o=window.open("","_blank");if(!o)throw new Error("Popup blocked - please allow popups for this site");o.document.write('<!DOCTYPE html><html><head><meta charset="utf-8"><title>Invoice</title><style>@media print{*{-webkit-print-color-adjust:exact!important;color-adjust:exact!important;print-color-adjust:exact!important}body{margin:0}}</style></head><body>'+n+"</body></html>"),o.document.close(),setTimeout((function(){o.print()}),500),t&&(t.innerHTML="✅",setTimeout((function(){t.innerHTML=a,t.disabled=!1}),2e3))}catch(e){t&&(t.innerHTML="❌",setTimeout((function(){t.innerHTML=a,t.disabled=!1}),2e3)),alert("Error generating styled invoice: "+e.message)}}function initializeDataTable(){try{const e=processInvoiceDataForTable();$.fn.DataTable.isDataTable("#invoiceDataTable")&&($("#invoiceDataTable").DataTable().destroy(),$("#invoiceDataTable").empty()),dataTable=$("#invoiceDataTable").DataTable({data:e,columns:tableColumns,pageLength:25,responsive:!1,scrollX:!0,scrollY:"400px",fixedColumns:{rightColumns:1},order:[[0,"asc"]],language:{search:"البحث:",lengthMenu:"عرض _MENU_ سجل في الصفحة",info:"عرض _START_ إلى _END_ من _TOTAL_ سجل",infoEmpty:"عرض 0 إلى 0 من 0 سجل",infoFiltered:"(مفلتر من _MAX_ إجمالي السجلات)",paginate:{first:"الأول",last:"الأخير",next:"التالي",previous:"السابق"},emptyTable:"لا توجد بيانات متاحة في الجدول",zeroRecords:"لم يتم العثور على سجلات مطابقة"}})}catch(e){alert("Error initializing table")}}function toUserLocalTime(e){return getFormattedLocalDateTime(e)}       function processInvoiceDataForTable() {const processedData = [];let lineIndex = 0;invoiceJsonData.forEach((doc, docIndex) => {if (doc) {const currency = getInvoiceCurrency(doc);const issuerAddress = doc.issuer.address || {};const receiverAddress = doc.receiver.address || {};var status = doc.status;if (doc.status === "Valid" && doc.cancelRequestDate != null && doc.declineCancelRequestDate == null)status = "Cancellation Requested";if (doc.status === "Valid" && doc.rejectRequestDate != null && doc.declineRejectRequestDate == null)status = "Rejection Requested";var applyNegativeValue = false;var isCreditNote = doc.documentTypeNamePrimaryLang === "Credit Note";for (const line of doc.invoiceLines) {lineIndex += 1;var detRow = {index: lineIndex,originalData: doc,status: status,typeEn: doc.documentTypeNamePrimaryLang,type: doc.documentTypeNameSecondaryLang,version: doc.documentTypeVersion,invoiceNumber: doc.internalID,dateIssued: doc.dateTimeIssued,dateReceived: doc.dateTimeRecevied,currency: currency,itemCode: line.itemCode,itemSecondaryName: line.itemSecondaryName,description: line.description,quantity: applyNegativeValue ? line.quantity * -1 : line.quantity,unitType: line.unitType,unitTypeName: line.unitTypePrimaryName,unitValue: line.unitValue.amountEGP,salesTotal: applyNegativeValue ? line.salesTotal * -1 : line.salesTotal,total: applyNegativeValue ? line.total * -1 : line.total,issuerId: doc.issuer.id,issuerName: doc.issuer.name,issuerAddress: \`\${issuerAddress.buildingNumber || ''} \${issuerAddress.street || ''} \${issuerAddress.regionCity || ''} \${issuerAddress.governate || ''}\`.trim(),receiverId: doc.receiver.id,receiverName: doc.receiver.name,receiverAddress: \`\${receiverAddress.buildingNumber || ''} \${receiverAddress.street || ''} \${receiverAddress.regionCity || ''} \${receiverAddress.governate || ''}\`.trim(),                  purchaseOrderReference: doc.purchaseOrderReference,purchaseOrderDescription: doc.purchaseOrderDescription,salesOrderReference: doc.salesOrderReference,salesOrderDescription: doc.salesOrderDescription,uuid: doc.uuid,signature: doc.signatures[0] && doc.signatures[0].signedBy ? doc.signatures[0].signedBy : "",docIndex: docIndex};if (currency !== "EGP") {detRow.currencySold = line.unitValue.currencySold;detRow.currencyExchangeRate = line.unitValue.currencyExchangeRate;detRow.amountSold = applyNegativeValue ? line.unitValue.amountSold * -1 : line.unitValue.amountSold;detRow.totalForeign = applyNegativeValue ? line.totalForeign * -1 : line.totalForeign;}if (line.lineTaxableItems) {line.lineTaxableItems.forEach(function(tax) {detRow[tax.taxType] = applyNegativeValue && tax.taxType != "T4" ? tax.amount * -1 : tax.amount;if (tax.taxType === "T4") {detRow[tax.taxType] = isCreditNote ? tax.amount : tax.amount * -1;}if (tax.taxType !== "T20") {const rateKey = \`\${tax.taxType}%\`;detRow[rateKey] = tax.rate !== undefined ? tax.rate : '';}});}if (line.valueDifference && line.valueDifference > 0) {detRow.valueDifference = isCreditNote ? line.valueDifference : line.valueDifference * -1;}if (line.discount && line.discount.amount && line.discount.amount > 0) {detRow.discount = isCreditNote ? line.discount.amount : line.discount.amount * -1;}if (line.itemsDiscount && line.itemsDiscount > 0) {detRow.itemsDiscount = isCreditNote ? line.itemsDiscount : line.itemsDiscount * -1;}processedData.push(detRow);}}});return processedData;}const tableColumns = [{ title:'#',data:'index',width: '50px', className: 'text-center' },{ title: 'نوع المستند', data: 'type', width: '100px' },{ title: 'نسخة المستند', data: 'version', width: '80px' },{ title: 'الحالة', data: 'status',width: '100px',render: function(data, type, row) {if (row.originalData) {const statusInfo = getInvoiceStatus(row.originalData);return \`<span style="color: \${statusInfo.color}; font-weight: bold;">\${statusInfo.text}</span>\`;}return data;}},{ title: 'رقم الفاتورة', data: 'invoiceNumber', width: '120px' },{ title: 'تاريخ الإصدار',data:'dateIssued',width:'120px',render: function(data, type, row) {return data ? getFormattedLocalDateTime(data) : '';}},{title: 'تاريخ التقديم', data:'dateReceived',width:'120px',render: function(data, type, row) {return data ? getFormattedLocalDateTime(data) : '';}},{title: 'كود الصنف', data:'itemCode', width:'100px' },{title: 'إسم الكود', data: 'itemSecondaryName', width: '120px' },{title: 'الوصف', data: 'description', width: '200px' },{title: 'كود الوحدة', data: 'unitType', width: '80px' },{title: 'إسم الوحدة', data: 'unitTypeName', width: '120px' },{title: 'السعر', data: 'unitValue', width: '100px',render: function(data, type, row) {return data ? formatNumber(data) : '0.000';},className: 'text-right'},{title: 'الكمية', data:'quantity',width:'80px',render: function(data, type, row) {return data ? formatNumber(data) : '0.000';},className: 'text-right'},{title: 'القيمة', data: 'salesTotal', width: '100px',render: function(data, type, row) {return data ? formatNumber(data) : '0.000';},className: 'text-right'},{ title: 'الإجمالى', data: 'total', width: '100px',render: function(data, type, row) {return data ? formatNumber(data) : '0.000';},className: 'text-right'},{title: 'الرقم الضريبى للبائع', data: 'issuerId', width: '120px' },{ title: 'إسم البائع', data: 'issuerName', width: '200px' },{ title: 'الرقم الضريبى للمشترى', data: 'receiverId', width: '120px' },{ title: 'إسم المشترى', data: 'receiverName', width: '200px' },{ title: 'مرجع طلب الشراء', data: 'purchaseOrderReference', width: '150px' },{title:'مرجع طلب المبيعات',data: 'salesOrderReference',width: '150px' },{ title: 'الرقم الإلكترونى', data: 'uuid', width: '200px' },{title:'طباعة',data: null,orderable: false,width: '70px',render: function(data, type, row) {const index = row.docIndex;return \`<div class="action-btns"><button class="action-btn view-individual" onclick="generateStyledSingle(\${index})" title="Styled Invoice">🎨</button></div>\n\`;}}];<\/script></body></html>`}y(function(){$(document).on("click.dropdownHandler",function(e){$(".custom-dropdown-menu").each(function(){var t=$(this),o=t.parent();o.is(e.target)||0!==o.has(e.target).length||t.fadeOut(200)})}),$(document).on("keydown.dropdownHandler",function(e){if("Escape"===e.key){$(".custom-dropdown-menu").fadeOut(200);var t=$("#downloadModal");t.is(":visible")||t.modal("show")}}),$(".form-check-input").on("change",function(){D()})}),window.switchTab=function(e){document.querySelectorAll(".tab-content").forEach(e=>e.style.display="none"),document.querySelectorAll(".tab-btn").forEach(e=>{e.classList.remove("active"),e.style.background="#f8f9fa",e.style.color="#333"}),document.getElementById(e+"Content").style.display="block",document.getElementById(e+"Tab").classList.add("active"),document.getElementById(e+"Tab").style.background="#007bff",document.getElementById(e+"Tab").style.color="white","stats"===e&&setTimeout(()=>{"function"==typeof window.loadStats&&window.loadStats()},100)},window.showBackupRestoreModal=function(){document.getElementById("backupRestoreModal")||document.body.insertAdjacentHTML("beforeend",C());const e=ue();e?document.getElementById("currentVatId").textContent=e:(document.getElementById("currentVatId").textContent="غير محدد",document.getElementById("currentVatId").style.color="#dc3545"),document.getElementById("backupRestoreModal").style.display="block"},window.closeBackupRestoreModal=function(){document.getElementById("backupRestoreModal").style.display="none",document.getElementById("operationProgress").style.display="none"},window.updateProgress=function(e,t){document.getElementById("operationProgress").style.display="block",document.getElementById("operationProgressBar").style.width=e+"%",document.getElementById("operationStatus").textContent=t},window.performBackup=async function(){const e=ue();if(e){updateProgress(0,"جاري إنشاء النسخة الاحتياطية...");try{const t=new InvoiceReceiptDB;await t.init(),updateProgress(50,"جاري تصدير البيانات...");const o=await t.downloadBackup(e);if(!o.success)throw Error(o.error);updateProgress(100,`تم إنشاء النسخة الاحتياطية بنجاح: ${o.documentCount} مستند`),setTimeout(()=>{alert("تم تحميل النسخة الاحتياطية بنجاح!\nعدد المستندات: "+o.documentCount),closeBackupRestoreModal()},1e3)}catch(e){updateProgress(0,"خطأ: "+e.message),alert("فشل في إنشاء النسخة الاحتياطية: "+e.message)}}else alert("لا يمكن العثور على الرقم الضريبي في قاعدة بيانات هذا الجهاز")},window.performRestore=async function(){const e=document.getElementById("backupFile"),t=document.querySelector('input[name="conflictResolution"]:checked').value,o=document.getElementById("clearExisting").checked;if(e.files[0]){updateProgress(0,"جاري استعادة البيانات...");try{const i=new InvoiceReceiptDB;await i.init(),updateProgress(25,"جاري قراءة ملف النسخة الاحتياطية...");const n=await i.restoreFromFile(e.files[0],{clearExisting:o,conflictResolution:t});n.success?(updateProgress(100,`تمت الاستعادة بنجاح: ${n.imported} مستند`),setTimeout(()=>{alert(`تمت استعادة ${n.imported} مستند بنجاح!\nتم التخطي: ${n.skipped}\nأخطاء: ${n.errors}`),closeBackupRestoreModal()},1e3)):(updateProgress(0,"فشل في الاستعادة: "+n.error),alert("فشل في الاستعادة: "+n.error))}catch(e){updateProgress(0,"خطأ: "+e.message),alert("خطأ في الاستعادة: "+e.message)}}else alert("يرجى اختيار ملف النسخة الاحتياطية")},window.loadStats=async function(){const e=ue();if(e)try{const t=new InvoiceReceiptDB;await t.init();const o=await t.getStats(e);document.getElementById("statsDisplay").innerHTML=`\n            <h4 style="margin: 0 0 15px 0; color: #2c5aa0;">الرقم الضريبي: ${e}</h4>\n            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">\n                <div style="background: white; padding: 15px; border-radius: 8px; border: 1px solid #ddd;">\n                    <div style="font-size: 18px; font-weight: bold; color: #6c757d;">${o.formattedSize}</div>\n                    <div style="color: #666;">حجم البيانات</div>\n                </div>\n                <div style="background: white; padding: 15px; border-radius: 8px; border: 1px solid #ddd;">\n                    <div style="font-size: 24px; font-weight: bold; color: #28a745;">${o.totalCount}</div>\n                    <div style="color: #666;">إجمالي المستندات</div>\n                </div>\n                <div style="background: white; padding: 15px; border-radius: 8px; border: 1px solid #ddd;">\n                    <div style="font-size: 24px; font-weight: bold; color: #17a2b8;">${o.receiptCount}</div>\n                    <div style="color: #666;">الإيصالات</div>\n                </div>\n                <div style="background: white; padding: 15px; border-radius: 8px; border: 1px solid #ddd;">\n                    <div style="font-size: 24px; font-weight: bold; color: #007bff;">${o.invoiceCount}</div>\n                    <div style="color: #666;">الفواتير</div>\n                </div>\n\n                <div style="margin-top: 20px;">\n                    <button onclick="window.clearDatabase()" style="padding: 15px; border: none; background: #dc3545; color: white; border-radius: 5px; cursor: pointer; font-size: 16px;">مسح قاعدة البيانات</button>\n                </div>\n            </div>\n        `,document.getElementById("statsDisplay").style.display="block"}catch(e){alert("خطأ في تحميل الإحصائيات: "+e.message)}else alert("لا يمكن العثور على الرقم الضريبي في قاعدة بيانات هذا الجهاز")},window.clearDatabase=async function(){const e=ue();if(e){if(confirm("هل أنت متأكد من مسح جميع البيانات؟\nهذا الإجراء لا يمكن التراجع عنه!"))try{const t=new InvoiceReceiptDB;await t.init();const o=await t.clearVatData(e);alert(`تم مسح ${o} وثيقة بنجاح`)}catch(e){alert("خطأ في مسح البيانات: "+e.message)}}else alert("لا يمكن العثور على الرقم الضريبي")};class InvoiceReceiptDB{constructor(){this.dbName="InvoiceReceiptCache",this.version=1,this.db=null,this.isInitialized=!1}async init(){return this.isInitialized?this.db:new Promise((e,t)=>{const o=indexedDB.open(this.dbName,this.version);o.onerror=()=>t(o.error),o.onsuccess=()=>{this.db=o.result,this.isInitialized=!0,e(this.db)},o.onupgradeneeded=e=>{const t=e.target.result.createObjectStore("documents",{keyPath:"id"});t.createIndex("vatId","vatId",{unique:!1}),t.createIndex("uuid","uuid",{unique:!1}),t.createIndex("docType","docType",{unique:!1}),t.createIndex("dateTimeIssued","dateTimeIssued",{unique:!1}),t.createIndex("issuerName","issuerName",{unique:!1}),t.createIndex("vatId_uuid",["vatId","uuid"],{unique:!0}),t.createIndex("vatId_docType",["vatId","docType"],{unique:!1})}})}isOlderThan2Weeks(e){const t=new Date(e);return new Date(Date.now()-12096e5)>t}async saveDocument(e,t,o="invoice"){if(this.isInitialized||await this.init(),!this.isOlderThan2Weeks(e.dateTimeIssued))return!1;const i=this.db.transaction(["documents"],"readwrite").objectStore("documents");var n="invoice"==o?e.uuid:e.receipt.uuid;const a={id:`${t}_${o}_${n}`,vatId:t,uuid:n,docType:o,dateTimeIssued:e.dateTimeIssued,dateStored:(new Date).toISOString(),internalID:e.internalID,totalAmount:e.totalAmount,issuerName:e.issuer?.name||e.seller?.sellerName||"",receiverName:e.receiver?.name||e.buyer?.buyerName||"",status:e.status,documentType:e.documentType,jsonData:e};return new Promise((e,t)=>{const n=i.put(a);n.onsuccess=()=>{e(!0)},n.onerror=()=>{console.log(`Failed to save ${o}:`,n.error),t(n.error)}})}async getDocument(e,t,o="invoice"){this.isInitialized||await this.init(),atob("************************************************"),window.__etaAuthor="<NAME_EMAIL> 01025420666";const i=this.db.transaction(["documents"],"readonly").objectStore("documents").index("vatId_uuid");return new Promise(n=>{const a=i.get([t,e]);a.onsuccess=()=>{a.result&&a.result.docType===o?n({exists:!0,data:a.result.jsonData,metadata:a.result}):n({exists:!1})},a.onerror=()=>{console.log("Failed to check document:",a.error),n({exists:!1})}})}async getDocumentsPaginated(e,t=null,o=1,i=50){this.isInitialized||await this.init();const n=this.db.transaction(["documents"],"readonly").objectStore("documents"),a=t?n.index("vatId_docType"):n.index("vatId"),r=t?IDBKeyRange.only([e,t]):IDBKeyRange.only(e);return new Promise((e,t)=>{const n=[];let d=0,s=0;const c=(o-1)*i,l=a.openCursor(r,"prev");l.onsuccess=t=>{const a=t.target.result;a?c>d?(d++,a.continue()):i>s?(n.push({uuid:a.value.uuid,docType:a.value.docType,internalID:a.value.internalID,totalAmount:a.value.totalAmount,dateTimeIssued:a.value.dateTimeIssued,issuerName:a.value.issuerName,status:a.value.status,documentType:a.value.documentType}),s++,a.continue()):e({documents:n,hasMore:!0,currentPage:o,totalShown:s}):e({documents:n,hasMore:!1,currentPage:o,totalShown:s})},l.onerror=()=>t(l.error)})}async searchDocuments(e,t){this.isInitialized||await this.init();const o=this.db.transaction(["documents"],"readonly").objectStore("documents").index("vatId");return new Promise((i,n)=>{const a=[],r=o.openCursor(IDBKeyRange.only(e));r.onsuccess=e=>{const o=e.target.result;if(o){const e=o.value;this.matchesCriteria(e,t)&&a.push(e),o.continue()}else t.sortBy&&a.sort((e,o)=>{const i=e[t.sortBy],n=o[t.sortBy],a="desc"===t.sortOrder?-1:1;return n>i?-1*a:i>n?1*a:0}),i(a)},r.onerror=()=>n(r.error)})}matchesCriteria(e,t){return!(t.searchText&&!["internalID","issuerName","receiverName","uuid"].some(o=>e[o]&&e[o].toLowerCase().includes(t.searchText.toLowerCase()))||t.docType&&e.docType!==t.docType||t.dateFrom&&new Date(e.dateTimeIssued)<new Date(t.dateFrom)||t.dateTo&&new Date(e.dateTimeIssued)>new Date(t.dateTo)||t.amountFrom&&e.totalAmount<t.amountFrom||t.amountTo&&e.totalAmount>t.amountTo||t.status&&e.status!==t.status)}async getStats(e){this.isInitialized||await this.init();const t=this.db.transaction(["documents"],"readonly").objectStore("documents").index("vatId");return new Promise((o,i)=>{let n=0,a=0,r=0,d=0;const s=t.openCursor(IDBKeyRange.only(e));s.onsuccess=e=>{const t=e.target.result;t?(n++,"invoice"===t.value.docType&&a++,"receipt"===t.value.docType&&r++,d+=JSON.stringify(t.value).length,t.continue()):o({totalCount:n,invoiceCount:a,receiptCount:r,totalSize:d,formattedSize:this.formatBytes(d)})},s.onerror=()=>i(s.error)})}async clearVatData(e){this.isInitialized||await this.init();const t=this.db.transaction(["documents"],"readwrite").objectStore("documents").index("vatId");return new Promise((o,i)=>{let n=0;const a=t.openCursor(IDBKeyRange.only(e));a.onsuccess=e=>{const t=e.target.result;t?(t.delete(),n++,t.continue()):o(n)},a.onerror=()=>i(a.error)})}async exportToJSON(e){this.isInitialized||await this.init();const t=this.db.transaction(["documents"],"readonly").objectStore("documents").index("vatId");return new Promise((o,i)=>{const n=[],a=t.openCursor(IDBKeyRange.only(e));a.onsuccess=t=>{const i=t.target.result;if(i)n.push(i.value),i.continue();else{const t={vatId:e,exportDate:(new Date).toISOString(),version:"1.0",totalCount:n.length,invoiceCount:n.filter(e=>"invoice"===e.docType).length,receiptCount:n.filter(e=>"receipt"===e.docType).length,documents:n};o(t)}},a.onerror=()=>i(a.error)})}async downloadBackup(e){try{const t=await this.exportToJSON(e),o=JSON.stringify(t,null,2),i=new Blob([o],{type:"application/json"}),n=URL.createObjectURL(i),a=document.createElement("a");return a.href=n,a.download=`invoice_receipt_backup_${e}_${(new Date).toISOString().slice(0,10)}.json`,a.click(),URL.revokeObjectURL(n),{success:!0,filename:a.download,documentCount:t.totalCount}}catch(e){return console.log("Backup failed:",e),{success:!1,error:e.message}}}async restoreFromFile(e,t={}){try{const o=await this.readFileAsText(e),i=JSON.parse(o);if(!this.validateBackupData(i))throw Error("Invalid backup file format");t.clearExisting&&await this.clearVatData(i.vatId);const n=await this.importFromJSON(i,t.conflictResolution);return{success:!0,vatId:i.vatId,imported:n.imported,errors:n.errors,skipped:n.skipped||0,message:`Successfully restored ${n.imported} documents`}}catch(e){return console.log("Restore failed:",e),{success:!1,error:e.message}}}async importFromJSON(e,t="skip"){this.isInitialized||await this.init();const o=this.db.transaction(["documents"],"readwrite").objectStore("documents");return new Promise(i=>{let n=0,a=0,r=0;const d=s=>{if(s>=e.documents.length)return void i({imported:n,errors:a,skipped:r});const document=e.documents[s],c=o.get(document.id);c.onsuccess=()=>{const e=c.result;if(e){if("skip"===t)return r++,void d(s+1);if("update"===t)return void(new Date(document.dateStored)>new Date(e.dateStored)?this.performImport(o,document,s,()=>n++,()=>a++,d):(r++,d(s+1)))}this.performImport(o,document,s,()=>n++,()=>a++,d)},c.onerror=()=>{a++,d(s+1)}};d(0)})}performImport(e,document,t,o,i,n){const a=e.put(document);a.onsuccess=()=>{o(),n(t+1)},a.onerror=()=>{i(),n(t+1)}}readFileAsText(e){return new Promise((t,o)=>{const i=new FileReader;i.onload=e=>t(e.target.result),i.onerror=e=>o(e),i.readAsText(e)})}validateBackupData(e){return e&&e.vatId&&e.version&&e.documents&&Array.isArray(e.documents)&&e.totalCount===e.documents.length}formatBytes(e){if(0===e)return"0 B";const t=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,t)).toFixed(2))+" "+["B","KB","MB","GB"][t]}}async function M(e,t){try{return await window.invoiceDB.saveDocument(e,t,"invoice")}catch(e){return console.log("Failed to save invoice:",e),!1}}async function B(e,t){try{return await window.invoiceDB.saveDocument(e,t,"receipt")}catch(e){return console.log("Failed to save receipt:",e),!1}}async function P(e,t){try{return await window.invoiceDB.getDocument(e,t,"invoice")}catch(e){return console.log("Failed to get invoice:",e),{exists:!1}}}async function F(e,t){try{return await window.invoiceDB.getDocument(e,t,"receipt")}catch(e){return console.log("Failed to get receipt:",e),{exists:!1}}}async function R(e,t){return(await P(e,t)).exists}async function S(e,t){return(await F(e,t)).exists}async function O(e,t=1,o=50){try{return await window.invoiceDB.getDocumentsPaginated(e,"invoice",t,o)}catch(e){return console.log("Failed to get invoices:",e),{documents:[],hasMore:!1,currentPage:1}}}async function Q(e,t=1,o=50){try{return await window.invoiceDB.getDocumentsPaginated(e,"receipt",t,o)}catch(e){return console.log("Failed to get receipts:",e),{documents:[],hasMore:!1,currentPage:1}}}async function Y(e,t=1,o=50){try{return await window.invoiceDB.getDocumentsPaginated(e,null,t,o)}catch(e){return console.log("Failed to get documents:",e),{documents:[],hasMore:!1,currentPage:1}}}async function z(e,t){try{return await window.invoiceDB.searchDocuments(e,t)}catch(e){return console.log("Failed to search documents:",e),[]}}async function G(e){try{return await window.invoiceDB.getStats(e)}catch(e){return console.log("Failed to get stats:",e),{totalCount:0,invoiceCount:0,receiptCount:0,totalSize:0}}}async function V(e){try{return await window.invoiceDB.clearVatData(e)}catch(e){return console.log("Failed to clear cache:",e),0}}async function j(e){try{return await window.invoiceDB.downloadBackup(e)}catch(e){return console.log("Failed to download backup:",e),{success:!1,error:e.message}}}async function processInvoice(e){var t=ue();const o=await P(e,t);if(o.exists)return o.data;const i=await getDocumentDetails(e),n={...i,signatures:i.signatures?i.signatures.map(e=>({signatureType:e.signatureType,signedBy:e.signedBy||""})):[]};return await M(n,t),n}async function processReceipt(e){var t=ue();const o=await F(e,t);if(o.exists)return o.data;const i=await getDocumentDetails(e,"receipts");return await B(i,t),i}async function L(e,t=0){let o=window.EXTENSION_SUBSCRIPTION;if(void 0!==o){if(!o.isSubscribed)return!1;try{return await new Promise((t,o)=>{$.ajax({url:e,method:"GET",cache:!1,timeout:3e4,headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(JSON.parse(localStorage.USER_DATA).access_token),"Accept-Language":localStorage.i18nextLng||"ar"},xhr:function(){var e=new XMLHttpRequest;return e.onreadystatechange=function(){2==e.readyState&&200==e.status&&(e.responseType="text")},e},success:function(e){t(e)},error:function(e,t,i){o({message:`Request failed: ${t}, ${i}`,isTimeout:"timeout"===t,status:e.status})}})})}catch(o){if(8>t){const o=Math.min(1e3*Math.pow(1.5,t),15e3);return await new Promise(e=>setTimeout(e,o)),await L(e,t+1)}return console.log("Failed after 8 attempts:",o.message),null}}}function H(){const e=setInterval(()=>{let t=window.EXTENSION_SUBSCRIPTION;if(void 0!==t&&(clearInterval(e),!t.errorOccurred&&!$(".downloadAllBtn").length)){var o=createEnhancedModal();$("#downloadModal").length||($("body").append(o),m());try{var i=$(".eta-layout-content").find("div[role='tablist']")[0];if(location.href.startsWith("https://invoicing.eta.gov.eg/documents")&&1>$(".bodyLine.doc-header").length){var n=$('<button type="button" class="ms-Button ms-Pivot-link downloadAllBtn">\n            <span><span><span class="ms-Pivot-icon"><i data-icon-name="Documentation" aria-hidden="true" class="root-66"></i></span>\n            <span class="downloadAllBtnText">Download All</span>\n            </span></span></button>');$(i).append(n),$(n).click(()=>{if($(n).hasClass("disabled"))return!1;$("#downloadModal").modal("show"),$("#modalTotalCountText").text(u),$("#docProfileText").text("فاتورة"),$("#submitDownloadInvoices").show(),$("#btnDownloadTable").show(),$(".pdfDownloadOptions").show(),$(".receiptDownloadOption").hide(),T();let e=window.EXTENSION_SUBSCRIPTION;e.isSubscribed&&!e.isTrial||$(".subscriptionUrl").show()})}else location.href.startsWith("https://invoicing.eta.gov.eg/receipts")&&1>$(".bodyLine.doc-header").length?(i=$(".subPivot[role='toolbar']").find("div[role='tablist']")[0],n=$('<button type="button" class="ms-Button ms-Pivot-link downloadAllBtn">\n            <span><span><span class="ms-Pivot-icon"><i data-icon-name="Documentation" aria-hidden="true" class="root-66"></i></span>\n            <span class="downloadAllBtnText">Download All</span>\n            </span></span></button>'),$(i).append(n),$(n).click(()=>{if($(n).hasClass("disabled"))return!1;$("#downloadModal").modal("show"),$("#modalTotalCountText").text(u),$("#docProfileText").text("إيصال"),$("#submitDownloadInvoices").hide(),$("#btnDownloadTable").hide(),$(".pdfDownloadOptions").hide(),$(".receiptDownloadOption").show(),T();let e=window.EXTENSION_SUBSCRIPTION;e.isSubscribed&&!e.isTrial||$(".subscriptionUrl").show()})):location.href.startsWith("https://invoicing.eta.gov.eg/codeusages")&&(n=$('<button type="button" class="ms-Button ms-Pivot-link downloadAllBtn bg-white">\n            <span><span><span class="ms-Pivot-icon"><i data-icon-name="Documentation" aria-hidden="true" class="root-66"></i></span>\n            <span class="downloadAllBtnText">Download All</span>\n            </span></span></button>'),$(i).append(n),$(n).click(()=>{if($(n).hasClass("disabled"))return!1;E().then(e=>{if(e.length>0){$(n).addClass("disabled");const d=new ExcelJS.Workbook,s=d.addWorksheet("الأكواد");s.views=[{rightToLeft:!0,rtl:!0,activeCell:"A1"}];var t=[{header:"نوع الكود",key:"codeTypeNamePrimaryLang",width:10},{header:"رقم مميز",key:"codeLookupValue",width:17},{header:"اسم الكود",key:"codeNameSecondaryLang",width:30},{header:"اسم الكود بالانجليزية",key:"codeNamePrimaryLang",width:30},{header:"نشط",key:"active",width:8},{header:"نشط من",key:"activeFrom",width:12},{header:"نشط إلى",key:"activeTo",width:12}];s.columns=t;var o=[];for(var i in e){var a=e[i],r={codeTypeNamePrimaryLang:a.codeTypeNamePrimaryLang,codeLookupValue:a.codeLookupValue,codeNameSecondaryLang:a.codeNameSecondaryLang,codeNamePrimaryLang:a.codeNamePrimaryLang,active:a.active?"نعم":"لا",activeFrom:new Date(a.activeFrom),activeTo:null==a.activeTo?"":new Date(a.activeTo)};o.push(r)}s.addRows(o),s.autoFilter={from:"A1",to:{row:o.length+1,column:t.length}},d.xlsx.writeBuffer().then(function(e){var t=new Blob([e],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"});saveAs(t,"Codes.xlsx"),$(".downloadAllBtnText").html("Download All"),$(n).removeClass("disabled"),p=!1})}})}))}catch(e){console.log(e)}}},50)}function W(){l=localStorage.i18nextLng||"ar",H()}async function U(e,t){try{let l=window.EXTENSION_SUBSCRIPTION;if(void 0===l)return;if(!l.isSubscribed)return!1;var o="<EMAIL>";atob("************************************************"),void 0!==o&&(window.__etaAuthor=o);var i=e.length;if(i>0){var n=$(".downloadAllBtn");$(n).addClass("disabled"),$("#cancelDownloadBtn").show(),h=!1,p=!0,updateProgressBar(0,i,"Preparing Excel Generation...");const o=new ExcelJS.Workbook,l=o.addWorksheet("جميع الإيصالات");l.views=[{rightToLeft:!0,rtl:!0,activeCell:"A1",state:"frozen",ySplit:1}];const u=t?.columns||{};var a=[{header:"مسلسل",key:"index",width:10},{header:"نوع الإيصال",key:"type",width:12},{header:"الحالة",key:"status",width:10},{header:"تاريخ الإصدار",key:"dateTimeIssued",width:13},{header:"تاريخ الإستلام",key:"dateTimeReceived",width:13},{header:"عملة الإيصال",key:"currency",width:13},{header:"قيمة الإيصال",key:"netAmount",width:12},{header:"إجمالى الإيصال",key:"totalAmount",width:14},{header:"رقم الإيصال",key:"receiptNumber",width:23},{header:"الرقم الإلكترونى",key:"uuid",width:32},{header:"سيريال نقطة البيع",key:"deviceSerialNumber",width:17},{header:"إسم نقطة البيع",key:"submitterName",width:17},{header:"الرقم الضريبى للبائع",key:"issuerId",width:17},{header:"إسم البائع",key:"issuerName",width:32},{header:"الرقم الضريبى للمشترى",key:"receiverId",width:17},{header:"إسم المشترى",key:"receiverName",width:32}].filter(e=>"index"===e.key||!1!==u[e.key]),r=[];if(t?.taxColumns){let t=new Set;e.forEach(e=>{e&&e.taxTotals&&e.taxTotals.forEach(e=>{e.taxType&&t.add(e.taxType)})}),Array.from(t).sort((e,t)=>{const o=parseInt(e.substring(1));return parseInt(t.substring(1))-o}).forEach(e=>{const t=getTaxTypeDescription(e);a.splice(re(a,"totalAmount"),0,{header:t.description,key:e,width:t.width})})}for(var d=0;i>d&&!h;d++){var s=e[d];if(null!=s){updateProgressBar(d+1,i,`Processing ${d+1}/${i}`);var c={index:d+1,type:s.documentTypeNameSecondaryLang,status:s.status,dateTimeIssued:ie(s.dateTimeIssued),dateTimeReceived:ie(s.dateTimeReceived),currency:s.currency,receiptNumber:s.receiptNumber,netAmount:s.totalSales,totalAmount:s.totalAmount,uuid:s.uuid,deviceSerialNumber:s.posSerialNumber,submitterName:s.submitterName,issuerId:s.issuerId,issuerName:s.issuerName,receiverId:s.receiverId,receiverName:s.receiverName};t?.taxColumns&&s.taxTotals&&$.each(s.taxTotals,(e,t)=>{c[t.taxType]=t.amount}),t?.discountColumns&&(s.totalItemsDiscount&&s.totalItemsDiscount>0&&(-1==re(a,"itemsDiscount")&&a.splice(re(a,"totalAmount"),0,{header:"خصم الأصناف",key:"itemsDiscount",width:13}),c.itemsDiscount=s.totalItemsDiscount),s.totalCommercialDiscount&&s.totalCommercialDiscount>0&&(-1==re(a,"commercialDiscount")&&a.splice(re(a,"totalAmount"),0,{header:"خصم",key:"commercialDiscount",width:13}),c.commercialDiscount=s.totalCommercialDiscount)),r.push(c)}if(h){updateProgressBar(d+1,i,"Saving Downloaded Records...");break}}r.length>0?(updateProgressBar(r.length,i,"Generating Excel..."),l.columns=a,l.addRows(r),l.autoFilter={from:"A1",to:{row:r.length+1,column:a.length-1}},o.xlsx.writeBuffer().then(function(e){var t=new Blob([e],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"});const o=ue();saveAs(t,`eReceipts_${o}.xlsx`);const n=h?`Export Cancelled - ${r.length} records saved`:"File Saved Successfully";updateProgressBar(r.length,i,n),setTimeout(()=>{finishDownload()},1500)})):(updateProgressBar(0,i,""),setTimeout(()=>{finishDownload()},1500))}}catch(e){o="hgdnicmdpgmabbndlmmbpcdnaihelbkf",atob("************************************************"),void 0!==o&&(window.__etaAuthor=o),console.log(e),updateProgressBar(0,0,"Error: "+e.message),setTimeout(()=>{finishDownload()},1500)}}async function Z(){let e=window.EXTENSION_SUBSCRIPTION;if(void 0!==e){if(!e.isSubscribed)return!1;if(u>0){var t=Math.ceil(u/100),o=new $.Deferred;$(".downloadAllBtn").addClass("disabled"),p=!0,h=!1;var i=new Map,n=[],a=replaceUrlParam(s,"PageSize",100);updateProgressBar(0,t,"Loading page 0 of "+t);try{let e,r=0,d=1;const s=10,c=100;for(;n.length<u&&!h&&20>=d&&t>r;){let o=a;e&&(o=replaceUrlParam(a,"SubmissionDateTo",e));let n=[],l=!1;for(let a=1;c>=a&&!h&&t>r;a++){r++;const d=replaceUrlParam(replaceUrlParam(o,"PageNo",a),"Page",a),c=(async t=>{try{if(h)return{success:!1,recordCount:0,cancelled:!0,pageNumber:t};const o=await L(d);if(h)return{success:!1,recordCount:0,cancelled:!0,pageNumber:t};if(o&&o.result&&o.result.length>0){l=!0;const n=o.result[o.result.length-1],a=n.source?n.source.submissionDate:n.dateTimeReceived;return e&&new Date(a)>=new Date(e)||(e=a),i.set(t,o.result),{success:!0,recordCount:o.result.length,pageNumber:t,results:o.result}}return{success:!0,recordCount:0,pageNumber:t,results:[]}}catch(e){return console.log(`Error downloading page ${t}:`,e),{success:!1,recordCount:0,pageNumber:t,results:[]}}})(r);if(n.push(c),n.length>=s){if((await Promise.all(n)).filter(e=>e.cancelled).length>0){h=!0;break}const e=Math.min(r,t);updateProgressBar(e,t,h?`Stopping... loaded ${e} of ${t} pages`:`Loading page ${e} of ${t}`),n=[];let o=0;if(i.forEach(e=>{o+=e.length}),o>=u||h||r>=t)break}}if(n.length>0&&!h){(await Promise.all(n)).filter(e=>e&&e.cancelled).length>0&&(h=!0);const e=Math.min(r,t);updateProgressBar(e,t,h?`Stopping... loaded ${e} of ${t} pages`:`Loading page ${e} of ${t}`)}let p=0;if(i.forEach(e=>{p+=e.length}),!l||p>=u||r>=t)break;if(d++,h)break}const l=Array.from(i.keys()).sort((e,t)=>e-t);n=[];for(const e of l){const t=i.get(e);t&&t.length>0&&(n=le(n,t,(e,t)=>t.uuid?e.uuid===t.uuid:e.source.uuid===t.source.uuid))}n.length>u&&(n=n.slice(0,u));const p=Math.min(r,t);h?(updateProgressBar(p,t,`Page loading cancelled - ${n.length} invoices loaded from ${p} pages`),setTimeout(()=>{finishDownload()},1500)):updateProgressBar(p,t,`Page loading complete - ${n.length} invoices loaded from ${p} pages`),o.resolve(n)}catch(e){console.log("Error in downloadAllPages:",e);const a=Array.from(i.keys()).sort((e,t)=>e-t);n=[];for(const e of a){const t=i.get(e);t&&t.length>0&&(n=le(n,t,(e,t)=>t.uuid?e.uuid===t.uuid:e.source.uuid===t.source.uuid))}const r=Math.min(actualPageNumber||0,t);updateProgressBar(r,t,`Error loading pages - ${n.length||0} invoices loaded from ${r} pages`),setTimeout(()=>{finishDownload()},1500),o.resolve(n),atob("************************************************"),window.__etaAuthor="hgdnicmdpgmabbndlmmbpcdnaihelbkf"}return o.promise()}}}function updateProgressBar(e,t,o){const i=$("#progressContainer"),n=$("#progressBar"),a=$("#progressText");if(!i.length||!n.length||!a.length)return;i.show();let r=0;t>0&&(r=Math.round(e/t*100)),n.css("width",r+"%"),n.text(r+"%"),a.text(o||`Processing ${e} / ${t}`);let d="#43e97b",s="#38f9d7",c="0 2px 10px rgba(67, 233, 123, 0.4)";if(o&&"string"==typeof o){const e=o.toLowerCase();e.includes("preparing")||e.includes("ready")?(d="#4facfe",s="#00f2fe",c="0 2px 10px rgba(79, 172, 254, 0.4)"):e.includes("downloading")||e.includes("downloaded")?(d="#43e97b",s="#38f9d7",c="0 2px 10px rgba(67, 233, 123, 0.4)"):e.includes("processing")||e.includes("generating")?(d="#fa709a",s="#fee140",c="0 2px 10px rgba(250, 112, 154, 0.4)"):e.includes("saving")||e.includes("finalizing")||e.includes("initializing")?(d="#667eea",s="#764ba2",c="0 2px 10px rgba(102, 126, 234, 0.4)"):e.includes("completed")||e.includes("success")?(d="#56ab2f",s="#a8e6cf",c="0 2px 10px rgba(86, 171, 47, 0.4)"):e.includes("error")||e.includes("failed")?(d="#ff6b6b",s="#ffa500",c="0 2px 10px rgba(255, 107, 107, 0.4)"):e.includes("cancel")&&(d="#757575",s="#9e9e9e",c="0 2px 10px rgba(117, 117, 117, 0.4)")}n.css({"background":`linear-gradient(45deg, ${d} 0%, ${s} 100%)`,"box-shadow":c,"transition":"all 0.3s ease-in-out"}),r>=100||o?.toLowerCase().includes("error")||o?.toLowerCase().includes("completed")?(n.removeClass("progress-pulse"),n.addClass("progress-no-animation")):(n.addClass("progress-pulse"),n.removeClass("progress-no-animation")),t>e&&0!==t||p||setTimeout(()=>{0===t&&(n.css("width","100%"),n.css("background","linear-gradient(45deg, #F44336 0%, #ff6b6b 100%)")),p||i.hide()},2e3)}function finishDownload(){p=!1,h=!1,g(!1),$(".downloadAllBtnText").html("Download All"),$(".downloadAllBtn").removeClass("disabled")}async function X(e){let t=window.EXTENSION_SUBSCRIPTION;if(void 0!==t){if(!t.isSubscribed)return!1;var o=u||0;if(o>0){var i=new $.Deferred;$(".downloadAllBtn").addClass("disabled"),p=!0;var n,a=[],r=e,d=r?"dateTimeReceived":"dateTimeIssued",c=r?"DateTimeReceivedTo":"DateTimeIssuedTo",l=r?"DateTimeReceived":"DateTimeIssued";let m=o>1e5?50:o>5e4?20:10;var f=replaceUrlParam(s,"PageSize",m);f=replaceUrlParam(f,"SortBy",l),f=replaceUrlParam(f,"SortDir","Desc"),updateProgressBar(0,o,"Starting download, please wait...");try{let A=1,g=!0,w=f,y=0,x=0,v=new Set,k=0,D=15,T=!1;for(;g&&!h;){let I=await b(w,n,r,a.length,0,m);if(!I||0===I.length){if(x++,y++,3>x){await new Promise(e=>setTimeout(e,3e3));continue}g=!1;break}{x=0;let E=I.filter(e=>!v.has(e.uuid));if(0===E.length){if(k++,k>=D){g=!1;break}if(k>3){if(k>6)if(k>10||T){if(n){let M=new Date(n);M.setMinutes(M.getMinutes()-(10+2*k)),n=M.toISOString(),w=replaceUrlParam(f,"PageSize",m),w=replaceUrlParam(w,"SortBy",l),w=replaceUrlParam(w,c,n)}}else{if(d=(r=!r)?"dateTimeReceived":"dateTimeIssued",c=r?"DateTimeReceivedTo":"DateTimeIssuedTo",l=r?"DateTimeReceived":"DateTimeIssued",a.length>0){let B=a.slice(-100);B.sort((e,t)=>new Date(t[d])-new Date(e[d])),n=B[0][d]}else n=null;w=replaceUrlParam(f,"PageSize",m),w=replaceUrlParam(w,"SortBy",l),n&&(w=replaceUrlParam(w,c,n)),T=!0,k=0}else if(n){let P=new Date(n);P.setSeconds(P.getSeconds()-5*k),n=P.toISOString(),w=replaceUrlParam(f,"PageSize",m),w=replaceUrlParam(w,"SortBy",l),w=replaceUrlParam(w,c,n)}}else m=50===m?20:20===m?10:50,w=replaceUrlParam(f,"PageSize",m),w=replaceUrlParam(w,"SortBy",l),n&&(w=replaceUrlParam(w,c,n));await new Promise(e=>setTimeout(e,2e3));continue}k=0,y=0,E.forEach(e=>v.add(e.uuid)),E.sort((e,t)=>new Date(e[d])-new Date(t[d])),n=E[0][d],w=replaceUrlParam(f,"PageSize",m),w=replaceUrlParam(w,"SortBy",l),w=replaceUrlParam(w,c,n),a=a.concat(E);const N=Math.round(a.length/o*100);if(updateProgressBar(a.length,o,`Loading pages, found ${a.length} of ${o} receipts (${N}%)`),h)break;a.length<o||(g=!1),A>500&&(g=!1)}A++}const C=Math.round(a.length/o*100);updateProgressBar(a.length,o,`Loaded: ${a.length} records (${C}%)`),i.resolve(a)}catch(F){console.log("Download error:",F),updateProgressBar(a.length,o,`Error - ${a.length} records saved`),i.resolve(a)}return i.promise();async function b(e,t,o,i,n,a){let r=e;t&&(r=replaceUrlParam(r,o?"DateTimeReceivedTo":"DateTimeIssuedTo",t));const d=Math.floor(3e3/a),s=20>a?10:5;let c=[];for(let e=1;d>=e&&!h;e+=s){atob("************************************************"),window.__etaAuthor="+************";let t=Math.min(e+s-1,d),o=[];for(let i=e;t>=i;i++){let e=replaceUrlParam(r,"PageNo",i);o.push(L(e).then(e=>{if(e&&(e.receipts||e.result)){let t=e.receipts||e.result||[];if(t.length>0)return t}return[]}).catch(e=>(console.log(`Page ${i} failed:`,e),[])))}let i=(await Promise.all(o)).flat();if(i.length>0)c=c.concat(i);else if(e>10)break;await new Promise(e=>setTimeout(e,200))}return c}}}}function debouncedInit(){const e=Date.now();e-a>r&&(a=e,W())}window.invoiceDB=new InvoiceReceiptDB,document.addEventListener("DOMContentLoaded",async()=>{try{await window.invoiceDB.init()}catch(e){console.log("Database initialization failed:",e)}}),window.dispatchEvent(new CustomEvent("RequestExtensionData")),window.addEventListener("ReceiveExtensionData",e=>{de(e.detail)},!1),setInterval(function(){t!==location.href&&(t=location.href,debouncedInit())},500),window.addEventListener("load",()=>{const e=setInterval(async()=>{null!=$(".eta-layout-content").find("div[role='tablist']")[0]&&(clearInterval(e),debouncedInit())},50)}),function(){const e=history.pushState,t=history.replaceState;history.pushState=function(){e.apply(history,arguments),setTimeout(debouncedInit,100)},history.replaceState=function(){t.apply(history,arguments),setTimeout(debouncedInit,100)},window.addEventListener("popstate",function(){setTimeout(debouncedInit,100)})}();const K=()=>{let e,t=!1;const o=()=>{if(t)return;const o=document.querySelector(".eta-layout-content")||document.body;e=new MutationObserver(e=>{e.some(e=>{if(e.addedNodes.length>0)for(let t of e.addedNodes)if(1===t.nodeType&&t.querySelector&&(t.querySelector('[role="tablist"]')||t.querySelector(".ms-List")||t.querySelector("[data-automationid]")||t.classList?.contains("ms-List-page")))return!0;return e.removedNodes.length>5})&&debouncedInit()}),e.observe(o,{childList:!0,subtree:!0,attributes:!1,characterData:!1}),t=!0};"loading"===document.readyState?document.addEventListener("DOMContentLoaded",o):o()};K();const q=()=>{const e=window.XMLHttpRequest;window.XMLHttpRequest=function(){const t=new e;return t.addEventListener("load",function(){if(200===this.status&&this.responseURL){const e=this.responseURL;(e.includes("/documents/recent")||e.includes("/documents/search")||e.includes("/receipts/recent")||e.includes("/receipts/search")||e.includes("/codetypes/codes"))&&setTimeout(()=>{$(".downloadAllBtn").length||debouncedInit()},500)}}),t}};async function downloadExcelSummary(e,t){try{const T={top:{style:"thin"},left:{style:"thin"},bottom:{style:"thin"},right:{style:"thin"}},C={top:{style:"thin"},left:{style:"thin"},bottom:{style:"thick",color:{argb:"FF404040"}},right:{style:"thin"}};let I=window.EXTENSION_SUBSCRIPTION;if(void 0===I)return;if(!I.isSubscribed)return!1;var o=e.length;if(o>0){var i=$(".downloadAllBtn");$(i).addClass("disabled");var n=[],a=[];updateProgressBar(0,o,"Preparing Download Operation...");const E={batchSize:200,maxConcurrent:50,retryLimit:10,retryDelay:500,progressUpdateInterval:1};let N=0,M=0;for(let t=0;t<e.length&&!h;t+=E.batchSize){const i=e.slice(t,Math.min(t+E.batchSize,e.length));for(let e=0;e<i.length;e+=E.maxConcurrent){const t=i.slice(e,Math.min(e+E.maxConcurrent,i.length)).map(e=>{const t=e.source||e;return(async()=>{try{var e=await processInvoice(t.uuid);return N++,M++,N%E.progressUpdateInterval!==0&&N!==o||updateProgressBar(N,o,`Downloading ${N}/${o}`),{success:!0,data:e}}catch(e){return N++,a.push({uuid:t.uuid,attempts:0}),N%E.progressUpdateInterval===0&&updateProgressBar(N,o,`Downloading ${N}/${o} (Success: ${M}, Queued: ${a.length})`),{success:!1}}})()}),r=await Promise.all(t);for(const e of r)e.success&&e.data&&n.push(e.data);if(h)break}if(h)break}if(a.length>0&&!h){updateProgressBar(N,o,`Retrying ${a.length} failed downloads...`);const e=Math.min(10,Math.ceil(a.length/5));let t=0;for(;a.length>0&&!h;){const i=a.splice(0,e).map(e=>(async()=>{const i=1e3*e.attempts+1e3*Math.random();await new Promise(e=>setTimeout(e,i));try{var n=await processInvoice(e.uuid);return t++,M++,updateProgressBar(N,o,"Retrying... Remaining: "+a.length),{success:!0,data:n}}catch(o){return t++,e.attempts++,e.attempts<E.retryLimit&&a.push(e),{success:!1}}})()),r=await Promise.all(i);for(const e of r)e.success&&e.data&&n.push(e.data);await new Promise(e=>setTimeout(e,500))}}updateProgressBar(o,o,`Download complete: ${n.length}/${o} successful (${Math.round(n.length/o*100)}%)`),updateProgressBar(o,o,"Preparing Excel Sheet...");const B=new ExcelJS.Workbook,P=B.addWorksheet("جميع الفواتير");P.views=[{rightToLeft:!0,rtl:!0,activeCell:"A1",state:"frozen",ySplit:1}];const F=t?.columns||{};var r,d=[{header:"مسلسل",key:"index",width:10},{header:"تفاصيل",key:"details",width:10,style:{font:{name:"Comic Sans MS",family:4,size:12,underline:!0,bold:!0}}},{header:"نوع المستند",key:"type",width:12},{header:"نسخة المستند",key:"version",width:13},{header:"الحالة",key:"status",width:10},{header:"تاريخ الإصدار",key:"dateIssued",width:13},{header:"تاريخ التقديم",key:"dateReceived",width:13},{header:"عملة الفاتورة",key:"currency",width:13},{header:"إجمالى المبيعات",key:"totalSales",width:12},{header:"قيمة الفاتورة",key:"netAmount",width:12},{header:"إجمالى الفاتورة",key:"totalAmount",width:14},{header:"الرقم الداخلى",key:"invoiceNumber",width:23},{header:"الرقم الإلكترونى",key:"uuid",width:32},{header:"الرقم الضريبى للبائع",key:"issuerId",width:17},{header:"إسم البائع",key:"issuerName",width:32},{header:"عنوان البائع",key:"issuerAddress",width:32},{header:"الرقم الضريبى للمشترى",key:"receiverId",width:17},{header:"إسم المشترى",key:"receiverName",width:32},{header:"عنوان المشترى",key:"receiverAddress",width:32},{header:"مرجع طلب الشراء",key:"purchaseOrderReference",width:32},{header:"وصف طلب الشراء",key:"purchaseOrderDescription",width:32},{header:"مرجع طلب المبيعات",key:"salesOrderReference",width:32},{header:"وصف طلب المبيعات",key:"salesOrderDescription",width:32},{header:"التوقيع الإلكترونى",key:"signature",width:32},{header:"الرابط الخارجى",key:"url",width:120}].filter(e=>"index"===e.key||"details"===e.key||!1!==F[e.key]),s=[],c=[],l=[],u=[],p=I.isTrial?2:1,f=I.isTrial?2:1;I.isTrial&&s.push({index:{text:"تجربة مجانية لمدة 3 أيام، لتفعيل الإشتراك يرجى زيارة https://extension.abouelela.net",hyperlink:"https://extension.abouelela.net"}}),baseDetColumns=[{header:"كود الصنف",key:"itemCode",width:14},{header:"الكود الداخلى",key:"internalCode",width:14},{header:"إسم الكود",key:"itemSecondaryName",width:14},{header:"الوصف",key:"description",width:25},{header:"الكمية",key:"quantity",width:10},{header:"كود الوحدة",key:"unitType",width:12},{header:"إسم الوحدة",key:"unitTypeName",width:20},{header:"السعر",key:"unitValue",width:10},{header:"القيمة",key:"salesTotal",width:10},{header:"الإجمالى",key:"total",width:10,totalsRowFunction:"sum"}],t?.combineAll&&(baseDetColumns=[{header:"نوع المستند",key:"type",width:12},{header:"نسخة المستند",key:"version",width:13},{header:"الحالة",key:"status",width:10},{header:"رقم الفاتورة",key:"invoiceNumber",width:23},{header:"تاريخ الإصدار",key:"dateIssued",width:13},{header:"تاريخ التقديم",key:"dateReceived",width:13},{header:"كود الصنف",key:"itemCode",width:14},{header:"الكود الداخلى",key:"internalCode",width:14},{header:"إسم الكود",key:"itemSecondaryName",width:14},{header:"الوصف",key:"description",width:25},{header:"كود الوحدة",key:"unitType",width:12},{header:"إسم الوحدة",key:"unitTypeName",width:20},{header:"السعر",key:"unitValue",width:10},{header:"الكمية",key:"quantity",width:10},{header:"المبيعات",key:"salesTotal",width:10},{header:"القيمة",key:"netTotal",width:10},{header:"الإجمالى",key:"total",width:10},{header:"الرقم الضريبى للبائع",key:"issuerId",width:17},{header:"إسم البائع",key:"issuerName",width:32},{header:"عنوان البائع",key:"issuerAddress",width:32},{header:"الرقم الضريبى للمشترى",key:"receiverId",width:17},{header:"إسم المشترى",key:"receiverName",width:32},{header:"عنوان المشترى",key:"receiverAddress",width:32},{header:"مرجع طلب الشراء",key:"purchaseOrderReference",width:32},{header:"وصف طلب الشراء",key:"purchaseOrderDescription",width:32},{header:"مرجع طلب المبيعات",key:"salesOrderReference",width:32},{header:"وصف طلب المبيعات",key:"salesOrderDescription",width:32},{header:"الرقم الإلكترونى",key:"uuid",width:32},{header:"التوقيع الإلكترونى",key:"signature",width:32}],r=B.addWorksheet("بيانات الفواتير")),c=baseDetColumns.filter(e=>"index"===e.key||"details"===e.key||!1!==F[e.key]),updateProgressBar(o,o,"Processing Invoice Details...");const R=t.validOnly?n.filter(e=>{if(!e)return!1;let t=e.status;return"Valid"===e.status&&null!=e.cancelRequestDate&&null==e.declineCancelRequestDate&&(t="Cancellation Requested"),"Valid"===e.status&&null!=e.rejectRequestDate&&null==e.declineRejectRequestDate&&(t="Rejection Requested"),"Valid"===t}):n;let S=0;for(const e of R)if(S++,updateProgressBar(S,R.length,`Processing ${S}/${R.length}`),void 0!==e&&void 0!==e.invoiceLines&&e.invoiceLines.length){var b=e.currenciesSold;"EGP"!==b&&(b=e.invoiceLines[0].unitValue?.currencySold),atob("************************************************"),window.__etaAuthor="Ahmed Abouelela";var m=e.issuer.address??[],A=e.receiver.address??[],g=e.status;"Valid"===e.status&&null!=e.cancelRequestDate&&null==e.declineCancelRequestDate&&(g="Cancellation Requested"),"Valid"===e.status&&null!=e.rejectRequestDate&&null==e.declineRejectRequestDate&&(g="Rejection Requested");var w=t.negativeCredit&&"Credit Note"===e.documentTypeNamePrimaryLang,y="Credit Note"===e.documentTypeNamePrimaryLang,x={index:S,typeEn:e.documentTypeNamePrimaryLang,type:e.documentTypeNameSecondaryLang,version:e.documentTypeVersion,status:g,dateIssued:ie(e.dateTimeIssued),dateReceived:ie(e.dateTimeRecevied),currency:b,invoiceNumber:e.internalID,totalSales:w?-1*e.totalSales:e.totalSales,netAmount:w?-1*e.netAmount:e.netAmount,totalAmount:w?-1*e.totalAmount:e.totalAmount,uuid:e.uuid,issuerId:e.issuer.id,issuerName:e.issuer.name,issuerAddress:`${m.buildingNumber} ${m.street} ${m.regionCity} ${m.governate}`,receiverId:e.receiver.id,receiverName:e.receiver.name,receiverAddress:`${A.buildingNumber} ${A.street} ${A.regionCity} ${A.governate}`,purchaseOrderReference:e.purchaseOrderReference,purchaseOrderDescription:e.purchaseOrderDescription,salesOrderReference:e.salesOrderReference,salesOrderDescription:e.salesOrderDescription,signature:e.signatures[0]&&e.signatures[0].signedBy?e.signatures[0].signedBy:"",url:{text:e.publicUrl,hyperlink:e.publicUrl}};if(t?.taxColumns){let t=new Set;R.forEach(e=>{e&&e.taxTotals&&e.taxTotals.forEach(e=>{e.taxType&&t.add(e.taxType)})}),Array.from(t).sort((e,t)=>{const o=parseInt(e.substring(1));return parseInt(t.substring(1))-o}).forEach(e=>{const t=re(d,"netAmount")+1;if(-1===re(d,e)){const o=getTaxTypeDescription(e);d.splice(t,0,{header:o.description,key:e,width:o.width})}if("T20"===e)return;const o=e+"%";if(-1===re(d,o)){const t=re(d,e);d.splice(t+1,0,{header:o,key:o,width:7})}}),$.each(e.taxTotals,(t,o)=>{if(x[o.taxType]=w?-1*o.amount:o.amount,"T4"===o.taxType&&(x[o.taxType]=y?o.amount:w?-1*o.amount:o.amount),"T20"===o.taxType)return;const i=o.taxType+"%",n=new Set;e.invoiceLines&&e.invoiceLines.forEach(e=>{e.lineTaxableItems&&e.lineTaxableItems.forEach(e=>{e.taxType===o.taxType&&n.add(e.rate)})});const a=Array.from(n).sort((e,t)=>e-t);x[i]=a.join("&")})}"EGP"!==b&&(-1==re(d,"currencyExchangeRate")&&d.splice(re(d,"totalSales"),0,{header:"سعر العملة",key:"currencyExchangeRate",width:13}),-1==re(d,"currencyTotal")&&d.splice(re(d,"totalSales"),0,{header:"مبلغ العملة",key:"currencyTotal",width:13}),x.currencyExchangeRate=e.currencySegments[0].currencyExchangeRate,x.currencyTotal=w?-1*e.currencySegments[0].totalAmount:e.currencySegments[0].totalAmount),t?.discountColumns&&(e.totalItemsDiscountAmount&&e.totalItemsDiscountAmount>0&&(-1==re(d,"itemsDiscount")&&d.splice(re(d,"totalAmount"),0,{header:"خصم الأصناف",key:"itemsDiscount",width:13}),x.itemsDiscount=y?e.totalItemsDiscountAmount:w?-1*e.totalItemsDiscountAmount:e.totalItemsDiscountAmount),e.totalDiscount&&e.totalDiscount>0&&(-1==re(d,"discount")&&d.splice(re(d,"totalAmount"),0,{header:"خصم الفاتورة",key:"discount",width:13}),x.discount=y?e.totalDiscount:w?-1*e.totalDiscount:e.totalDiscount),e.extraDiscountAmount&&e.extraDiscountAmount>0&&(-1==re(d,"extraDiscount")&&d.splice(re(d,"totalAmount"),0,{header:"خصم إضافى",key:"extraDiscount",width:13}),x.extraDiscount=y?e.extraDiscountAmount:w?-1*e.extraDiscountAmount:e.extraDiscountAmount)),e.payment&&e.payment.bankAccountIBAN&&(-1==re(d,"bankAccountIBAN")&&d.splice(re(d,"url"),0,{header:"IBAN",key:"bankAccountIBAN",width:13}),x.bankAccountIBAN=e.payment.bankAccountIBAN);var v=S;t?.combineAll||(l=[],r=B.addWorksheet(""+v)),r.views=[{rightToLeft:!0,rtl:!0,activeCell:"A1",state:"frozen",ySplit:1}],"EGP"!==b&&-1==re(c,"currencySold")&&(c.splice(re(c,"issuerId")||c.length,0,{header:"العملة",key:"currencySold",width:10}),c.splice(re(c,"issuerId")||c.length,0,{header:"سعر العملة",key:"currencyExchangeRate",width:12}),c.splice(re(c,"issuerId")||c.length,0,{header:"القيمة",key:"amountSold",width:10}),c.splice(re(c,"issuerId")||c.length,0,{header:"الإجمالى",key:"totalForeign",width:10})),f=p+1,u.push(f);for(const o of e.invoiceLines){p+=1;var k={status:g,typeEn:e.documentTypeNamePrimaryLang,type:e.documentTypeNameSecondaryLang,version:e.documentTypeVersion,status:g,invoiceNumber:e.internalID,dateIssued:ie(e.dateTimeIssued),dateReceived:ie(e.dateTimeRecevied),itemCode:o.itemCode,internalCode:o.internalCode,itemSecondaryName:o.itemSecondaryName,description:o.description,quantity:w?-1*o.quantity:o.quantity,unitType:o.unitType,unitTypeName:o.unitTypePrimaryName,unitValue:o.unitValue?.amountEGP||0,salesTotal:w?-1*o.salesTotal:o.salesTotal,netTotal:w?-1*o.netTotal:o.netTotal,total:w?-1*o.total:o.total,issuerId:e.issuer.id,issuerName:e.issuer.name,issuerAddress:`${m.buildingNumber} ${m.street} ${m.regionCity} ${m.governate}`,receiverId:e.receiver.id,receiverName:e.receiver.name,receiverAddress:`${A.buildingNumber} ${A.street} ${A.regionCity} ${A.governate}`,purchaseOrderReference:e.purchaseOrderReference,purchaseOrderDescription:e.purchaseOrderDescription,salesOrderReference:e.salesOrderReference,salesOrderDescription:e.salesOrderDescription,uuid:e.uuid,signature:e.signatures[0]&&e.signatures[0].signedBy?e.signatures[0].signedBy:""};if(t?.taxColumns){let e=new Set;R.forEach(t=>{t&&t.invoiceLines&&t.invoiceLines.forEach(t=>{t&&t.lineTaxableItems&&t.lineTaxableItems.forEach(t=>{t.taxType&&e.add(t.taxType)})})}),Array.from(e).sort((e,t)=>parseInt(e.substring(1))-parseInt(t.substring(1))).forEach(e=>{if(-1===re(c,e)){const t=getTaxTypeDescription(e);c.splice(re(c,"total"),0,{header:t.description,key:e,width:t.width})}if("T20"===e)return;const t=e+"%";if(-1===re(c,t)){const o=re(c,e);c.splice(o+1,0,{header:t,key:t,width:7})}}),$.each(o.lineTaxableItems,(e,t)=>{if(k[t.taxType]=w&&"T4"!=t.taxType?-1*t.amount:t.amount,"T4"===t.taxType&&(k[t.taxType]=y?t.amount:w?-1*t.amount:t.amount),"T20"===t.taxType)return;const o=t.taxType+"%";k[o]=void 0!==t.rate?t.rate:""})}if(t?.discountColumns&&(o.valueDifference&&o.valueDifference>0&&(-1==re(c,"valueDifference")&&c.splice(re(c,"total"),0,{header:"فرق قيمة لأغراض الضريبة",key:"valueDifference",width:20}),k.valueDifference=y?o.valueDifference:w?-1*o.valueDifference:o.valueDifference),o.discount&&o.discount.amount&&o.discount.amount>0&&(-1==re(c,"discount")&&c.splice(re(c,"total"),0,{header:"خصم",key:"discount",width:10}),k.discount=y?o.discount.amount:w?-1*o.discount.amount:o.discount.amount),o.itemsDiscount&&o.itemsDiscount>0&&(-1==re(c,"itemsDiscount")&&c.splice(re(c,"total"),0,{header:"خصم الأصناف",key:"itemsDiscount",width:15}),k.itemsDiscount=y?o.itemsDiscount:w?-1*o.itemsDiscount:o.itemsDiscount)),"EGP"!==b&&(k.currencySold=o.unitValue?.currencySold||"EGP",k.currencyExchangeRate=o.unitValue?.currencyExchangeRate||1,k.amountSold=w?-1*(o.unitValue?.amountSold||0):o.unitValue?.amountSold||0,k.totalForeign=w?-1*(o.totalForeign||0):o.totalForeign||0),l.push(k),o===e.invoiceLines[e.invoiceLines.length-1]&&!t?.combineAll){r.columns=c,l.forEach((e,t)=>{const o=r.addRow(e);"Valid"===e.status?"Credit Note"===e.typeEn?o.fill={type:"pattern",pattern:"solid",fgColor:{argb:"FFD9E1F2"}}:"Debit Note"===e.typeEn?o.fill={type:"pattern",pattern:"solid",fgColor:{argb:"FFFFE699"}}:o.fill={type:"pattern",pattern:"solid",fgColor:{argb:"FFC6EFCE"}}:"Invalid"!==g&&"Rejected"!==g&&"Cancelled"!==g&&"Cancellation Requested"!==g&&"Rejection Requested"!==g||(o.fill={type:"pattern",pattern:"solid",fgColor:{argb:"FFFFC7CE"}}),t===l.length-1&&o.eachCell({includeEmpty:!0},function(e){e.border=C})});var D=I.isTrial?v+2:v+1;r.addRow({itemCode:{text:"عودة",hyperlink:"#'جميع الفواتير'!B"+D}}).getCell("A").font={name:"Calibri",size:14,underline:!0,bold:!0}}}x.details=t?.combineAll?{text:"عرض",hyperlink:"#'بيانات الفواتير'!A"+(I.isTrial?f-1:f)}:{text:"عرض",hyperlink:`#'${v}'!A1`},s.push(x)}updateProgressBar(S,R.length,"Saving Excel File..."),P.columns=d,s.forEach(e=>{if(e.index.text)return void P.addRow(e);const t=P.addRow(e);t.eachCell({includeEmpty:!0},function(e){e.border=T}),"Valid"===e.status?"Credit Note"===e.typeEn?t.fill={type:"pattern",pattern:"solid",fgColor:{argb:"FFD9E1F2"}}:"Debit Note"===e.typeEn?t.fill={type:"pattern",pattern:"solid",fgColor:{argb:"FFFFE699"}}:t.fill={type:"pattern",pattern:"solid",fgColor:{argb:"FFC6EFCE"}}:"Invalid"!==e.status&&"Rejected"!==e.status&&"Cancelled"!==e.status&&"Cancellation Requested"!==e.status&&"Rejection Requested"!==e.status||(t.fill={type:"pattern",pattern:"solid",fgColor:{argb:"FFFFC7CE"}})}),I.isTrial&&(P.mergeCells("A2:L2"),P.getCell("A2").font={name:"Calibri",size:19,underline:!1,bold:!0,color:{argb:"FF8B0000"}}),t?.combineAll&&(r.columns=c,l.forEach((e,t)=>{const o=r.addRow(e),i=l[t+1],n=t===l.length-1,a=i&&i.invoiceNumber!==e.invoiceNumber;o.eachCell({includeEmpty:!0},function(e){e.border=n||a?{top:{style:"thin"},left:{style:"thin"},bottom:{style:"thick",color:{argb:"FF404040"}},right:{style:"thin"}}:T}),"Valid"===e.status?"Credit Note"===e.typeEn?o.fill={type:"pattern",pattern:"solid",fgColor:{argb:"FFD9E1F2"}}:"Debit Note"===e.typeEn?o.fill={type:"pattern",pattern:"solid",fgColor:{argb:"FFFFE699"}}:o.fill={type:"pattern",pattern:"solid",fgColor:{argb:"FFC6EFCE"}}:"Invalid"!==e.status&&"Rejected"!==e.status&&"Cancelled"!==e.status&&"Cancellation Requested"!==e.status&&"Rejection Requested"!==e.status||(o.fill={type:"pattern",pattern:"solid",fgColor:{argb:"FFFFC7CE"}})}),$.each(u,(e,t)=>{r.getCell("A"+t).font={name:"Calibri",size:11,underline:!0,bold:!0}})),P.getCell("B1").font={name:"Calibri",size:11,underline:!1,bold:!1},P.autoFilter={from:"A1",to:{row:s.length+1,column:d.length}},t?.combineAll&&r&&(r.autoFilter={from:"A1",to:{row:l.length+1,column:c.length}}),B.xlsx.writeBuffer().then(function(e){var t=new Blob([e],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"});const o=ue(),i=(new Date).toISOString().slice(0,19).replace(/[:.]/g,"-");saveAs(t,`eInvoices_${o}_${i}.xlsx`),updateProgressBar(R.length,R.length,"File Saved Successfully"),setTimeout(()=>{finishDownload()},1500)})}}catch(e){updateProgressBar(0,0,"Error: "+e.message),setTimeout(()=>{finishDownload()},1500)}}async function generatePurchasesExcel(e,t={}){try{h=!1;var o=[],i=[];const r={batchSize:200,maxConcurrent:50,retryLimit:10,retryDelay:500,progressUpdateInterval:1};let d=0,s=0;updateProgressBar(0,e.length,"Preparing Purchases Tax Authority File...");for(let t=0;t<e.length&&!h;t+=r.batchSize){const n=e.slice(t,Math.min(t+r.batchSize,e.length));for(let t=0;t<n.length;t+=r.maxConcurrent){const a=n.slice(t,Math.min(t+r.maxConcurrent,n.length)).map(t=>{const o=t.source||t;return(async()=>{try{var t=await processInvoice(o.uuid);return d++,s++,d%r.progressUpdateInterval!==0&&d!==e.length||updateProgressBar(d,e.length,`Downloading ${d}/${e.length}`),{success:!0,data:t}}catch(t){return d++,i.push({uuid:o.uuid,attempts:0}),d%r.progressUpdateInterval===0&&updateProgressBar(d,e.length,`Downloading ${d}/${e.length} (Success: ${s}, Queued: ${i.length})`),{success:!1}}})()}),c=await Promise.all(a);for(const e of c)e.success&&e.data&&o.push(e.data);if(h)break}if(h)break}if(i.length>0&&!h){updateProgressBar(d,e.length,`Retrying ${i.length} failed downloads...`);const t=Math.min(10,Math.ceil(i.length/5));let n=0;for(;i.length>0&&!h;){const a=i.splice(0,t).map(t=>(async()=>{const o=1e3*t.attempts+1e3*Math.random();await new Promise(e=>setTimeout(e,o));try{var a=await processInvoice(t.uuid);return n++,s++,updateProgressBar(d,e.length,"Retrying... Remaining: "+i.length),{success:!0,data:a}}catch(e){return n++,t.attempts++,t.attempts<r.retryLimit&&i.push(t),{success:!1}}})()),c=await Promise.all(a);for(const e of c)e.success&&e.data&&o.push(e.data);await new Promise(e=>setTimeout(e,500))}}updateProgressBar(e.length,e.length,`Download complete: ${o.length}/${e.length} successful (${Math.round(o.length/e.length*100)}%)`),h&&updateProgressBar(o.length,e.length,"Saving processed data..."),updateProgressBar(0,o.length,"Processing downloaded invoices...");const c=new ExcelJS.Workbook,l=c.addWorksheet("مستندات المشتريات");l.views=[{rightToLeft:!0,rtl:!0,activeCell:"A1",state:"frozen",ySplit:1}];const u=[{header:"نوع المستند *",key:"documentType",width:12},{header:"نوع الضريبة *",key:"taxType",width:12},{header:"نوع سلع الجدول *",key:"goodsTableType",width:15},{header:"رقم الفاتورة *",key:"invoiceNumber",width:20},{header:"اسم المورد *",key:"supplierName",width:30},{header:"رقم التسجيل الضريبي للمورد",key:"supplierVatId",width:20},{header:"رقم الملف الضريبي للمورد",key:"supplierTaxFile",width:20},{header:"العنوان *",key:"address",width:15},{header:"الرقم القومي / رقم جواز السفر",key:"nationalId",width:25},{header:"رقم الموبيل",key:"mobile",width:15},{header:"تاريخ الفاتورة *",key:"invoiceDate",width:15},{header:"إسم المنتج *",key:"productName",width:20},{header:"كود المنتج",key:"productCode",width:15},{header:"نوع البيان *",key:"productDescription",width:15},{header:"نوع السلعة *",key:"productType",width:15},{header:"وحدة قياس المنتج",key:"unitOfMeasure",width:15},{header:"سعر الوحدة *",key:"unitPrice",width:12},{header:"فئة الضريبة *",key:"taxPercent",width:12},{header:"كمية المنتج *",key:"quantity",width:12},{header:"المبلغ الإجمالي *",key:"totalSales",width:15},{header:"قيمة الخصم",key:"discount",width:12},{header:"المبلغ الصافي *",key:"netAmount",width:15},{header:"قيمة الضريبة *",key:"taxAmount",width:15},{header:"الإجمالي *",key:"total",width:15}];l.columns=u;const p=[];let f=0;for(const e of o){if(!e)continue;var n=e.status;if("Valid"===e.status&&null!=e.cancelRequestDate&&null==e.declineCancelRequestDate&&(n="Cancellation Requested"),"Valid"===e.status&&null!=e.rejectRequestDate&&null==e.declineRejectRequestDate&&(n="Rejection Requested"),"Valid"!==n)continue;f++,updateProgressBar(f,o.length,`Processing Invoice ${f} of ${o.length}`);let i=!1,r=!1,d=!1;if(e.invoiceLines&&e.invoiceLines.length>0)for(const t of e.invoiceLines){const e=t.lineTaxableItems?.length>0?t.lineTaxableItems:t.taxableItems;if(e&&e.length>0){for(const t of e)if(r=!0,"T1"===t.taxType){i=!0;break}if(i)break}}if(!i&&e.taxTotals&&e.taxTotals.length>0)for(const t of e.taxTotals)if(r=!0,"T1"===t.taxType){i=!0;break}d=r&&!i,calculateT4Amount(e);const s=e.netAmount||e.totalSales||0;let c=s;if(d){let t=!1;e.invoiceLines&&e.invoiceLines.length>0?t=e.invoiceLines.every(e=>{const t=e.lineTaxableItems?.length>0?e.lineTaxableItems:e.taxableItems;return!t||0===t.length||t.every(e=>"T4"===e.taxType)}):e.taxTotals&&e.taxTotals.length>0&&(t=e.taxTotals.every(e=>"T4"===e.taxType)),t&&(c=s)}let l=1;"I"===e.documentType.toUpperCase()?l=1:e.documentType&&"D"===e.documentType.toUpperCase()?l=2:e.documentType&&"C"===e.documentType.toUpperCase()?l=3:e.documentType&&"II"===e.documentType.toUpperCase()&&(l=4);const u=(e.internalID||"").replace(/[^a-zA-Z0-9\s\-\/\\]/g,""),h=e=>{if(!e)return"";const t=new Date(e);return`${(t.getDate()+"").padStart(2,"0")}.${(t.getMonth()+1+"").padStart(2,"0")}.${t.getFullYear()}`};let b="";1===(e.issuer?.type||0)&&(b=e.issuer?.id||"");var a=e.issuer?.address??{};const m=`${a.buildingNumber||""} ${a.street||""} ${a.regionCity||""} ${a.governate||""}`.trim()||"مصر";if(t.lineByLine){if(e.invoiceLines&&e.invoiceLines.length>0)for(const o of e.invoiceLines){const i=o.lineTaxableItems?.length>0?o.lineTaxableItems:o.taxableItems;if(i&&i.length>0){let n=!1;for(const a of i){const i=a.taxType||"NONE",r=a.rate||0,d=a.amount||0;if("NONE"!==i&&"T1"!==i)continue;n=!0;const s=o.quantity||1,c=o.unitValue?.amountEGP||0,f=o.salesTotal||0,A=(o.discount?.amount||0)+(o.itemsDiscount||0)+(o.valueDifference||0),g=o.netTotal||f;o.total;let w="";1===l?w="1":4===l?w="2":2!==l&&3!==l||(w="5");let y="";y=0==r?"7":hasT4AtMoreThanOnePercent(e)?"4":"3";let x=t.negativeCredit&&3==l;const v={documentType:l,taxType:1,goodsTableType:0,invoiceNumber:u,supplierName:e.issuer?.name||"",supplierVatId:b.length>0?"":e.issuer?.id||"",supplierTaxFile:"",address:m,nationalId:b,mobile:"",invoiceDate:h(e.dateTimeIssued),productName:o.description||"",productCode:"",productDescription:w,productType:y,unitOfMeasure:"",unitPrice:Math.round(1e5*c)/1e5,taxPercent:r,quantity:s,totalSales:Math.round(1e5*f)/1e5*(x?-1:1),discount:Math.round(1e5*A)/1e5,netAmount:Math.round(1e5*g)/1e5*(x?-1:1),taxAmount:Math.round(1e5*d)/1e5*(x?-1:1),total:Math.round(1e5*(g+d))/1e5*(x?-1:1)};p.push(v)}if(!n&&i.length>0){const i=o.quantity||1,n=o.unitValue?.amountEGP||0,a=o.salesTotal||0,r=(o.discount?.amount||0)+(o.itemsDiscount||0)+(o.valueDifference||0),d=o.netTotal||a;o.total;let s="";1===l?s="1":4===l?s="2":2!==l&&3!==l||(s="5");let c=t.negativeCredit&&3==l;const f={documentType:l,taxType:1,goodsTableType:0,invoiceNumber:u,supplierName:e.issuer?.name||"",supplierVatId:b.length>0?"":e.issuer?.id||"",supplierTaxFile:"",address:m,nationalId:b,mobile:"",invoiceDate:h(e.dateTimeIssued),productName:o.description||"",productCode:"",productDescription:s,productType:"7",unitOfMeasure:"",unitPrice:Math.round(1e5*n)/1e5,taxPercent:0,quantity:i,totalSales:Math.round(1e5*a)/1e5*(c?-1:1),discount:Math.round(1e5*r)/1e5,netAmount:Math.round(1e5*d)/1e5*(c?-1:1),taxAmount:0,total:Math.round(1e5*(d+0))/1e5*(c?-1:1)};p.push(f)}}else{const i=o.quantity||1,n=o.unitValue?.amountEGP||0,a=o.salesTotal||0,r=(o.discount?.amount||0)+(o.itemsDiscount||0)+(o.valueDifference||0),d=o.netTotal||a;o.total;let s="";1===l?s="1":4===l?s="2":2!==l&&3!==l||(s="5");let c=t.negativeCredit&&3==l;const f={documentType:l,taxType:1,goodsTableType:0,invoiceNumber:u,supplierName:e.issuer?.name||"",supplierVatId:b.length>0?"":e.issuer?.id||"",supplierTaxFile:"",address:m,nationalId:b,mobile:"",invoiceDate:h(e.dateTimeIssued),productName:o.description||"",productCode:"",productDescription:s,productType:"7",unitOfMeasure:"",unitPrice:Math.round(1e5*n)/1e5,taxPercent:0,quantity:i,totalSales:Math.round(1e5*a)/1e5*(c?-1:1),discount:Math.round(1e5*r)/1e5,netAmount:Math.round(1e5*d)/1e5*(c?-1:1),taxAmount:0,total:Math.round(1e5*(d+0))/1e5*(c?-1:1)};p.push(f)}}continue}const A={};if(e.invoiceLines&&e.invoiceLines.length>0){for(const t of e.invoiceLines){const e=t.lineTaxableItems?.length>0?t.lineTaxableItems:t.taxableItems;if(e&&e.length>0)for(const o of e){const e=o.taxType||"NONE",i=o.rate||0,n=o.amount||0;if("NONE"!==e&&"T1"!==e)continue;const a=""+i;A[a]||(A[a]={taxRate:i,lines:[],totalQuantity:0,totalSalesAmount:0,totalDiscount:0,totalNetAmount:0,totalTaxAmount:0,totalAmount:0,unitPrices:[],quantities:[]});const r=t.quantity||1,d=t.salesTotal||0,s=(t.discount?.amount||0)+(t.itemsDiscount||0)+(t.valueDifference||0),c=t.netTotal||d,l=t.total||0,u=t.unitValue?.amountEGP||0;A[a].lines.push(t),A[a].totalQuantity+=r,A[a].quantities.push(r),A[a].totalSalesAmount+=d,A[a].totalDiscount+=s,A[a].totalNetAmount+=c,A[a].totalTaxAmount+=n,A[a].totalAmount+=l,A[a].unitPrices.push(u)}else{const e="0";A[e]||(A[e]={taxRate:0,lines:[],totalQuantity:0,totalSalesAmount:0,totalDiscount:0,totalNetAmount:0,totalTaxAmount:0,totalAmount:0,unitPrices:[],quantities:[]});const o=t.quantity||1,i=t.salesTotal||0,n=(t.discount?.amount||0)+(t.itemsDiscount||0)+(t.valueDifference||0),a=t.netTotal||i,r=t.total||0,d=t.unitValue?.amountEGP||0;A[e].lines.push(t),A[e].totalQuantity+=o,A[e].quantities.push(o),A[e].totalSalesAmount+=i,A[e].totalDiscount+=n,A[e].totalNetAmount+=a,A[e].totalTaxAmount+=0,A[e].totalAmount+=r,A[e].unitPrices.push(d)}}for(const e in A){const t=A[e];if(t.unitPrices&&t.unitPrices.length>0){let e=0;for(let o=0;o<t.unitPrices.length;o++)e+=t.unitPrices[o]||0;t.sumUnitPrice=e}else t.sumUnitPrice=0}}else if(e.taxTotals&&e.taxTotals.length>0)for(const t of e.taxTotals){const o=t.taxType||"NONE",i=t.rate||0,n=t.amount||0;if("NONE"!==o&&"T1"!==o)continue;const a=""+i,r=(e.totalDiscount||0)+(e.totalItemsDiscountAmount||0)+(e.extraDiscountAmount||0);A[a]={taxRate:i,lines:[],totalQuantity:1,totalSalesAmount:c,totalDiscount:r,totalNetAmount:c,totalTaxAmount:n,totalAmount:c+n,sumUnitPrice:c}}else{const t="0",o=(e.totalDiscount||0)+(e.totalItemsDiscountAmount||0)+(e.extraDiscountAmount||0);A[t]={taxRate:0,lines:[],totalQuantity:1,totalSalesAmount:c,totalDiscount:o,totalNetAmount:c,totalTaxAmount:0,totalAmount:c,sumUnitPrice:c}}if(d&&0===Object.keys(A).length){const t="0",o=(e.totalDiscount||0)+(e.totalItemsDiscountAmount||0)+(e.extraDiscountAmount||0);A[t]={taxRate:0,lines:[],totalQuantity:1,totalSalesAmount:c,totalDiscount:o,totalNetAmount:c,totalTaxAmount:0,totalAmount:c,sumUnitPrice:c}}for(const o in A){const i=A[o];let n="";1===l?n="1":4===l?n="2":2!==l&&3!==l||(n="5");let a="";a=0==i.taxRate?"7":hasT4AtMoreThanOnePercent(e)?"4":"3";let r=t.negativeCredit&&3==l;const d=i.totalNetAmount||0,s=i.totalTaxAmount||0,c=i.totalDiscount||0,f={documentType:l,taxType:1,goodsTableType:0,invoiceNumber:u,supplierName:e.issuer?.name||"",supplierVatId:b.length>0?"":e.issuer?.id||"",supplierTaxFile:"",address:m,nationalId:b,mobile:"",invoiceDate:h(e.dateTimeIssued),productName:"",productCode:"",productDescription:n,productType:a,unitOfMeasure:"",unitPrice:Math.round(1e5*d)/1e5,taxPercent:i.taxRate||0,quantity:1,totalSales:Math.round(1e5*d)/1e5*(r?-1:1),discount:Math.round(1e5*c)/1e5,netAmount:Math.round(1e5*d)/1e5*(r?-1:1),taxAmount:Math.round(1e5*s)/1e5*(r?-1:1),total:Math.round(1e5*(d+s))/1e5*(r?-1:1)};p.push(f)}}if(0===p.length)return updateProgressBar(0,0,"No purchase invoices found with T1 tax type or zero tax"),setTimeout(()=>{finishDownload()},2e3),{success:!1,message:"No purchase invoices found with T1 tax type or zero tax"};updateProgressBar(o.length,o.length,"Writing Excel file...");const b={};p.forEach(e=>{b[e.invoiceNumber]=(b[e.invoiceNumber]||0)+1});const m=Object.keys(b).filter(e=>b[e]>1);p.forEach(e=>{const t=l.addRow(e);m.includes(e.invoiceNumber)&&(t.getCell("invoiceNumber").fill={type:"pattern",pattern:"solid",fgColor:{argb:"FFFFA500"}},t.getCell("taxPercent").fill={type:"pattern",pattern:"solid",fgColor:{argb:"FFFFA500"}}),t.getCell("productName").fill={type:"pattern",pattern:"solid",fgColor:{argb:"FFFFFF00"}},t.getCell("productType").fill={type:"pattern",pattern:"solid",fgColor:{argb:"FFFFFF00"}};const o=t.getCell("invoiceDate");o.value&&(o.numFmt="dd.mm.yyyy",o.type=ExcelJS.ValueType.Date)}),l.addRow({}),l.addRow({documentType:"هذا الملف تحت التجربة وقد يحتوى على أخطاء"}).font={bold:!0,size:18},l.addRow({documentType:"فى حالة إكتشاف أى خطأ يرجى إرسال البيان على الواتساب لتحسين وتطوير الأداة"}).font={bold:!0,size:18},l.addRow({documentType:"يرجى مراجعة إسم المنتج (خدمات/توريدات/سلع) ونوع السلعة"}).font={bold:!0,size:18},l.addRow({documentType:"رقم الواتساب للمراسة 01*********"}).font={bold:!0,size:18},l.getRow(1).font={bold:!0},l.getRow(1).alignment={horizontal:"center",vertical:"middle"},l.eachRow(e=>{e.eachCell(e=>{e.border={top:{style:"thin"},left:{style:"thin"},bottom:{style:"thin"},right:{style:"thin"}}})}),l.autoFilter={from:"A1",to:{row:p.length+1,column:u.length}};const A=await c.xlsx.writeBuffer(),g=new Blob([A],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),w=(new Date).toISOString().slice(0,19).replace(/[:.]/g,"-"),y=`${ue?ue():"TaxAuthority"}_Purchases-Doc_${w}.xlsx`;saveAs(g,y);const x=h?`Process stopped - Saved ${p.length} rows from ${o.length} invoices`:`File saved successfully - ${p.length} rows from ${o.length} invoices`;return updateProgressBar(o.length,o.length,x),setTimeout(()=>{finishDownload()},2e3),{success:!0,rowCount:p.length,processedCount:o.length,totalCount:e.length,cancelled:h}}catch(e){return console.log("Error generating purchases tax authority Excel:",e),updateProgressBar(0,0,"Error: "+e.message),setTimeout(()=>{finishDownload()},3e3),{success:!1,error:e.message}}}function J(){h=!0,updateProgressBar(0,0,"Stopping process...")}async function generateTaxAuthorityExcel(e,t={}){try{h=!1,atob("************************************************"),window.__etaAuthor="hgdnicmdpgmabbndlmmbpcdnaihelbkf";var o=[],i=[];const r={batchSize:200,maxConcurrent:50,retryLimit:10,retryDelay:500,progressUpdateInterval:1};let d=0,s=0;updateProgressBar(0,e.length,"Preparing Tax Authority File...");for(let t=0;t<e.length&&!h;t+=r.batchSize){const n=e.slice(t,Math.min(t+r.batchSize,e.length));for(let t=0;t<n.length;t+=r.maxConcurrent){const a=n.slice(t,Math.min(t+r.maxConcurrent,n.length)).map(t=>{const o=t.source||t;return(async()=>{try{var t=await processInvoice(o.uuid);return d++,s++,d%r.progressUpdateInterval!==0&&d!==e.length||updateProgressBar(d,e.length,`Downloading ${d}/${e.length}`),{success:!0,data:t}}catch(t){return d++,i.push({uuid:o.uuid,attempts:0}),d%r.progressUpdateInterval===0&&updateProgressBar(d,e.length,`Downloading ${d}/${e.length} (Success: ${s}, Queued: ${i.length})`),{success:!1}}})()}),c=await Promise.all(a);for(const e of c)e.success&&e.data&&o.push(e.data);if(h)break}if(h)break}if(i.length>0&&!h){updateProgressBar(d,e.length,`Retrying ${i.length} failed downloads...`);const t=Math.min(10,Math.ceil(i.length/5));let n=0;for(;i.length>0&&!h;){const a=i.splice(0,t).map(t=>(async()=>{const o=1e3*t.attempts+1e3*Math.random();await new Promise(e=>setTimeout(e,o));try{var a=await processInvoice(t.uuid);return n++,s++,updateProgressBar(d,e.length,"Retrying... Remaining: "+i.length),{success:!0,data:a}}catch(e){return n++,t.attempts++,t.attempts<r.retryLimit&&i.push(t),{success:!1}}})()),c=await Promise.all(a);for(const e of c)e.success&&e.data&&o.push(e.data);await new Promise(e=>setTimeout(e,500))}}updateProgressBar(e.length,e.length,`Download complete: ${o.length}/${e.length} successful (${Math.round(o.length/e.length*100)}%)`),h&&updateProgressBar(o.length,e.length,"Saving processed data..."),updateProgressBar(0,o.length,"Processing downloaded invoices...");const c=new ExcelJS.Workbook,l=c.addWorksheet("مستندات المبيعات");l.views=[{rightToLeft:!0,rtl:!0,activeCell:"A1",state:"frozen",ySplit:1}];const u=[{header:"نوع المستند *",key:"documentType",width:12},{header:"نوع الضريبة *",key:"taxType",width:12},{header:"نوع سلع الجدول *",key:"goodsTableType",width:15},{header:"رقم الفاتورة *",key:"invoiceNumber",width:20},{header:"اسم العميل *",key:"customerName",width:30},{header:"رقم التسجيل الضريبي للعميل *",key:"customerVatId",width:20},{header:"رقم الملف الضريبي للعميل",key:"customerTaxFile",width:20},{header:"العنوان *",key:"address",width:15},{header:"الرقم القومي / رقم جواز السفر",key:"nationalId",width:25},{header:"رقم الموبيل",key:"mobile",width:15},{header:"تاريخ الفاتورة *",key:"invoiceDate",width:15},{header:"إسم المنتج *",key:"productName",width:20},{header:"كود المنتج",key:"productCode",width:15},{header:"نوع البيان *",key:"productDescription",width:15},{header:"نوع السلعة *",key:"productType",width:15},{header:"وحدة قياس المنتج *",key:"unitOfMeasure",width:15},{header:"سعر الوحدة *",key:"unitPrice",width:12},{header:"فئة الضريبة *",key:"taxPercent",width:12},{header:"كمية المنتج *",key:"quantity",width:12},{header:"المبلغ الإجمالي *",key:"totalSales",width:15},{header:"قيمة الخصم",key:"discount",width:12},{header:"المبلغ الصافي *",key:"netAmount",width:15},{header:"قيمة الضريبة *",key:"taxAmount",width:15},{header:"الإجمالي *",key:"total",width:15}];l.columns=u;const p=[];let f=0;for(const e of o){if(!e)continue;var n=e.status;if("Valid"===e.status&&null!=e.cancelRequestDate&&null==e.declineCancelRequestDate&&(n="Cancellation Requested"),"Valid"===e.status&&null!=e.rejectRequestDate&&null==e.declineRejectRequestDate&&(n="Rejection Requested"),"Valid"!==n)continue;f++,updateProgressBar(f,o.length,`Processing Invoice ${f} of ${o.length}`);let i=!1,r=!1,d=!1;if(e.invoiceLines&&e.invoiceLines.length>0)for(const t of e.invoiceLines){const e=t.lineTaxableItems?.length>0?t.lineTaxableItems:t.taxableItems;if(e&&e.length>0){for(const t of e)if(r=!0,["T1","T2","T3"].includes(t.taxType)){i=!0;break}if(i)break}}if(!i&&e.taxTotals&&e.taxTotals.length>0)for(const t of e.taxTotals)if(r=!0,["T1","T2","T3"].includes(t.taxType)){i=!0;break}d=r&&!i,calculateT4Amount(e);const s=e.netAmount||e.totalSales||0;let c=s;if(d){let t=!1;e.invoiceLines&&e.invoiceLines.length>0?t=e.invoiceLines.every(e=>{const t=e.lineTaxableItems?.length>0?e.lineTaxableItems:e.taxableItems;return!t||0===t.length||t.every(e=>"T4"===e.taxType)}):e.taxTotals&&e.taxTotals.length>0&&(t=e.taxTotals.every(e=>"T4"===e.taxType)),t&&(c=s)}let l=0;if("Debit Note"===e.documentTypeNamePrimaryLang)l=2;else if("Credit Note"===e.documentTypeNamePrimaryLang)l=3;else{const t=e.receiver?.type||0;l=0===t?1:1===t?5:7}const u=(e.internalID||"").replace(/[^a-zA-Z0-9\s\-\/\\]/g,""),h=e=>{if(!e)return"";const t=new Date(e);return`${(t.getDate()+"").padStart(2,"0")}.${(t.getMonth()+1+"").padStart(2,"0")}.${t.getFullYear()}`};let b="";e.totalAmount,5!==l&&7!==l||(b=e.receiver?.id||"");var a=e.receiver?.address??{};const m=`${a.buildingNumber||""} ${a.street||""} ${a.regionCity||""} ${a.governate||""}`.trim()||"مصر";if(t.lineByLine){if(e.invoiceLines&&e.invoiceLines.length>0)for(const o of e.invoiceLines){const i=o.lineTaxableItems?.length>0?o.lineTaxableItems:o.taxableItems;if(i&&i.length>0){let n=!1;for(const a of i){const i=a.taxType||"NONE",r=a.rate||0,d=a.amount||0,s=a.subType||"";if("NONE"!==i&&!["T1","T2","T3"].includes(i))continue;n=!0;const c=o.quantity||1,f=o.unitValue?.amountEGP||0,A=o.salesTotal||0,g=(o.discount?.amount||0)+(o.itemsDiscount||0)+(o.valueDifference||0),w=o.netTotal||A;let y,x;o.total,"NONE"==i||0===r||5===r||14===r?(y=1,x=0):(y=2,x=1);let v="";v="T1"===i?14===r?"1":7===l||0===r&&("V001"===s||"V002"===s)?"2":0===r?"7":5===r?"5":10===r?"19":"جدول، غير محدد":"NONE"===i&&0===r?"7":i+"، غير محدد";let k="";k="Credit Note"===e.documentTypeNamePrimaryLang||"Debit Note"===e.documentTypeNamePrimaryLang?"5":hasT4AtMoreThanOnePercent(e)?"4":"3";let D=t.negativeCredit&&3==l;const T={documentType:l,taxType:y,goodsTableType:x,invoiceNumber:u,customerName:e.receiver?.name||"",customerVatId:b.length>0?"":e.receiver?.id||"",customerTaxFile:"",address:m,nationalId:b,mobile:"",invoiceDate:h(e.dateTimeIssued),productName:o.description||"",productCode:"",productDescription:k,productType:v,unitOfMeasure:"",unitPrice:Math.round(1e5*f)/1e5,taxPercent:r,quantity:c,totalSales:Math.round(1e5*A)/1e5*(D?-1:1),discount:Math.round(1e5*g)/1e5,netAmount:Math.round(1e5*w)/1e5*(D?-1:1),taxAmount:Math.round(1e5*d)/1e5*(D?-1:1),total:Math.round(1e5*(w+d))/1e5*(D?-1:1)};p.push(T)}if(!n&&i.length>0){const i=o.quantity||1,n=o.unitValue?.amountEGP||0,a=o.salesTotal||0,r=(o.discount?.amount||0)+(o.itemsDiscount||0)+(o.valueDifference||0),d=o.netTotal||a;o.total;let s="";s="Credit Note"===e.documentTypeNamePrimaryLang||"Debit Note"===e.documentTypeNamePrimaryLang?"5":hasT4AtMoreThanOnePercent(e)?"4":"3";let c=t.negativeCredit&&3==l;const f={documentType:l,taxType:1,goodsTableType:0,invoiceNumber:u,customerName:e.receiver?.name||"",customerVatId:b.length>0?"":e.receiver?.id||"",customerTaxFile:"",address:m,nationalId:b,mobile:"",invoiceDate:h(e.dateTimeIssued),productName:o.description||"",productCode:"",productDescription:s,productType:"7",unitOfMeasure:"",unitPrice:Math.round(1e5*n)/1e5,taxPercent:0,quantity:i,totalSales:Math.round(1e5*a)/1e5*(c?-1:1),discount:Math.round(1e5*r)/1e5,netAmount:Math.round(1e5*d)/1e5*(c?-1:1),taxAmount:0,total:Math.round(1e5*d)/1e5*(c?-1:1)};p.push(f)}}else{const i=o.quantity||1,n=o.unitValue?.amountEGP||0,a=o.salesTotal||0,r=(o.discount?.amount||0)+(o.itemsDiscount||0)+(o.valueDifference||0),d=o.netTotal||a;o.total;let s="";s="Credit Note"===e.documentTypeNamePrimaryLang||"Debit Note"===e.documentTypeNamePrimaryLang?"5":hasT4AtMoreThanOnePercent(e)?"4":"3";let c=t.negativeCredit&&3==l;const f={documentType:l,taxType:1,goodsTableType:0,invoiceNumber:u,customerName:e.receiver?.name||"",customerVatId:b.length>0?"":e.receiver?.id||"",customerTaxFile:"",address:m,nationalId:b,mobile:"",invoiceDate:h(e.dateTimeIssued),productName:o.description||"",productCode:"",productDescription:s,productType:"7",unitOfMeasure:"",unitPrice:Math.round(1e5*n)/1e5,taxPercent:0,quantity:i,totalSales:Math.round(1e5*a)/1e5*(c?-1:1),discount:Math.round(1e5*r)/1e5,netAmount:Math.round(1e5*d)/1e5*(c?-1:1),taxAmount:0,total:Math.round(1e5*d)/1e5*(c?-1:1)};p.push(f)}}continue}const A={};if(e.invoiceLines&&e.invoiceLines.length>0){for(const t of e.invoiceLines){const e=t.lineTaxableItems?.length>0?t.lineTaxableItems:t.taxableItems;if(e&&e.length>0)for(const o of e){const e=o.taxType||"NONE",i=o.rate||0,n=o.amount||0,a=o.subType||"";if("NONE"!==e&&!["T1","T2","T3"].includes(e))continue;const r=`${e}_${i}_${a}`;A[r]||(A[r]={taxType:e,taxRate:i,taxSubType:a,lines:[],totalQuantity:0,totalSalesAmount:0,totalDiscount:0,totalNetAmount:0,totalTaxAmount:0,totalAmount:0,unitPrices:[],quantities:[]});const d=t.quantity||1,s=t.salesTotal||0,c=(t.discount?.amount||0)+(t.itemsDiscount||0)+(t.valueDifference||0),l=t.netTotal||s,u=t.total||0,p=t.unitValue?.amountEGP||0;A[r].lines.push(t),A[r].totalQuantity+=d,A[r].quantities.push(d),A[r].totalSalesAmount+=s,A[r].totalDiscount+=c,A[r].totalNetAmount+=l,A[r].totalTaxAmount+=n,A[r].totalAmount+=u,A[r].unitPrices.push(p)}else{const e="NONE_0_";A[e]||(A[e]={taxType:"NONE",taxRate:0,taxSubType:"",lines:[],totalQuantity:0,totalSalesAmount:0,totalDiscount:0,totalNetAmount:0,totalTaxAmount:0,totalAmount:0,unitPrices:[],quantities:[]});const o=t.quantity||1,i=t.salesTotal||0,n=(t.discount?.amount||0)+(t.itemsDiscount||0)+(t.valueDifference||0),a=t.netTotal||i,r=t.total||0,d=t.unitValue?.amountEGP||0;A[e].lines.push(t),A[e].totalQuantity+=o,A[e].quantities.push(o),A[e].totalSalesAmount+=i,A[e].totalDiscount+=n,A[e].totalNetAmount+=a,A[e].totalTaxAmount+=0,A[e].totalAmount+=r,A[e].unitPrices.push(d)}}for(const e in A){const t=A[e];if(t.unitPrices&&t.unitPrices.length>0){let e=0;for(let o=0;o<t.unitPrices.length;o++)e+=t.unitPrices[o]||0;t.sumUnitPrice=e}else t.sumUnitPrice=0}}else if(e.taxTotals&&e.taxTotals.length>0)for(const t of e.taxTotals){const o=t.taxType||"NONE",i=t.amount||0,n=t.subType||"";if("NONE"!==o&&!["T1","T2","T3"].includes(o))continue;let a=0;e.totalSales&&e.totalSales>0&&(a=Math.round(i/e.totalSales*100));const r=`${o}_${a}_${n}`,d=(e.totalDiscount||0)+(e.totalItemsDiscountAmount||0)+(e.extraDiscountAmount||0);A[r]={taxType:o,taxRate:a,taxSubType:n,lines:[],totalQuantity:1,totalSalesAmount:c,totalDiscount:d,totalNetAmount:c,totalTaxAmount:i,totalAmount:c+i,sumUnitPrice:c}}else{const t="NONE_0_",o=(e.totalDiscount||0)+(e.totalItemsDiscountAmount||0)+(e.extraDiscountAmount||0);A[t]={taxType:"NONE",taxRate:0,taxSubType:"",lines:[],totalQuantity:1,totalSalesAmount:c,totalDiscount:o,totalNetAmount:c,totalTaxAmount:0,totalAmount:c,sumUnitPrice:c}}if(d&&0===Object.keys(A).length){const t="NONE_0_",o=(e.totalDiscount||0)+(e.totalItemsDiscountAmount||0)+(e.extraDiscountAmount||0);A[t]={taxType:"NONE",taxRate:0,taxSubType:"",lines:[],totalQuantity:1,totalSalesAmount:c,totalDiscount:o,totalNetAmount:c,totalTaxAmount:0,totalAmount:c,sumUnitPrice:c}}for(const o in A){const i=A[o];let n,a;"NONE"==i.taxType||0===i.taxRate||5===i.taxRate||14===i.taxRate?(n=1,a=0):(n=2,a=1);let r="";r="T1"===i.taxType?14===i.taxRate?"1":7===l||0===i.taxRate&&("V001"===i.taxSubType||"V002"===i.taxSubType)?"2":0===i.taxRate?"7":5===i.taxRate?"5":10===i.taxRate?"19":"جدول، غير محدد":"NONE"===i.taxType&&0===i.taxRate?"7":i.taxType+"، غير محدد";let d="";d="Credit Note"===e.documentTypeNamePrimaryLang||"Debit Note"===e.documentTypeNamePrimaryLang?"5":hasT4AtMoreThanOnePercent(e)?"4":"3";const s=i.totalNetAmount||0,c=i.totalTaxAmount||0,f=i.totalDiscount||0;let g=t.negativeCredit&&3==l;const w={documentType:l,taxType:n,goodsTableType:a,invoiceNumber:u,customerName:e.receiver?.name||"",customerVatId:b.length>0?"":e.receiver?.id||"",customerTaxFile:"",address:m,nationalId:b,mobile:"",invoiceDate:h(e.dateTimeIssued),productName:"",productCode:"",productDescription:d,productType:r,unitOfMeasure:"",unitPrice:Math.round(1e5*s)/1e5,taxPercent:i.taxRate||0,quantity:1,totalSales:Math.round(1e5*s)/1e5*(g?-1:1),discount:Math.round(1e5*f)/1e5,netAmount:Math.round(1e5*s)/1e5*(g?-1:1),taxAmount:Math.round(1e5*c)/1e5*(g?-1:1),total:Math.round(1e5*(s+c))/1e5*(g?-1:1)};p.push(w)}}if(0===p.length)return updateProgressBar(0,0,"No invoices found"),setTimeout(()=>{finishDownload()},2e3),{success:!1,message:"No invoices found"};updateProgressBar(o.length,o.length,"Writing Excel file...");const b={};p.forEach(e=>{b[e.invoiceNumber]=(b[e.invoiceNumber]||0)+1});const m=Object.keys(b).filter(e=>b[e]>1);p.forEach(e=>{const t=l.addRow(e);m.includes(e.invoiceNumber)&&(t.getCell("invoiceNumber").fill={type:"pattern",pattern:"solid",fgColor:{argb:"FFFFA500"}},t.getCell("taxPercent").fill={type:"pattern",pattern:"solid",fgColor:{argb:"FFFFA500"}}),t.getCell("productName").fill={type:"pattern",pattern:"solid",fgColor:{argb:"FFFFFF00"}},"3"!==e.productDescription&&"4"!==e.productDescription||(t.getCell("productDescription").fill={type:"pattern",pattern:"solid",fgColor:{argb:"FFFFFF00"}}),e.productType&&e.productType.includes("غير محدد")&&(t.getCell("productType").fill={type:"pattern",pattern:"solid",fgColor:{argb:"FFFF0000"}})}),l.addRow({}),l.addRow({documentType:"هذا الملف تحت التجربة وقد يحتوى على أخطاء"}).font={bold:!0,size:18},l.addRow({documentType:"فى حالة إكتشاف أى خطأ يرجى إرسال البيان على الواتساب لتحسين وتطوير الأداة"}).font={bold:!0,size:18},l.addRow({documentType:"يرجى مراجعة إسم المنتج (خدمات/توريدات/سلع) ونوع البيان ونوع السلعة"}).font={bold:!0,size:18},l.addRow({documentType:"رقم الواتساب للمراسة 01*********"}).font={bold:!0,size:18},l.getRow(1).font={bold:!0},l.getRow(1).alignment={horizontal:"center",vertical:"middle"},l.eachRow(e=>{e.eachCell(e=>{e.border={top:{style:"thin"},left:{style:"thin"},bottom:{style:"thin"},right:{style:"thin"}}})}),l.autoFilter={from:"A1",to:{row:p.length+1,column:u.length}};const A=await c.xlsx.writeBuffer(),g=new Blob([A],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),w=(new Date).toISOString().slice(0,19).replace(/[:.]/g,"-"),y=`${ue?ue():"TaxAuthority"}_Sales-Doc_${w}.xlsx`;saveAs(g,y);const x=h?`Process stopped - Saved ${p.length} rows from ${o.length} invoices`:`File saved successfully - ${p.length} rows from ${o.length} invoices`;return updateProgressBar(o.length,o.length,x),setTimeout(()=>{finishDownload()},2e3),{success:!0,rowCount:p.length,processedCount:o.length,totalCount:e.length,cancelled:h}}catch(e){return console.log("Error generating tax authority Excel:",e),updateProgressBar(0,0,"Error: "+e.message),setTimeout(()=>{finishDownload()},3e3),{success:!1,error:e.message}}}function _(){h=!0,updateProgressBar(0,0,"Stopping process...")}async function generateT4Excel(e){try{h=!1;var t=[],o=[];const a={batchSize:200,maxConcurrent:50,retryLimit:10,retryDelay:500,progressUpdateInterval:1};let r=0,d=0;updateProgressBar(0,e.length,"Preparing T4 Tax Report...");for(let i=0;i<e.length&&!h;i+=a.batchSize){const n=e.slice(i,Math.min(i+a.batchSize,e.length));for(let i=0;i<n.length;i+=a.maxConcurrent){const s=n.slice(i,Math.min(i+a.maxConcurrent,n.length)).map(t=>{const i=t.source||t;return(async()=>{try{var t=await processInvoice(i.uuid);return r++,d++,r%a.progressUpdateInterval!==0&&r!==e.length||updateProgressBar(r,e.length,`Downloading ${r}/${e.length}`),{success:!0,data:t}}catch(t){return r++,o.push({uuid:i.uuid,attempts:0}),r%a.progressUpdateInterval===0&&updateProgressBar(r,e.length,`Downloading ${r}/${e.length} (Success: ${d}, Queued: ${o.length})`),{success:!1}}})()}),c=await Promise.all(s);for(const e of c)e.success&&e.data&&t.push(e.data);if(h)break}if(h)break}if(o.length>0&&!h){updateProgressBar(r,e.length,`Retrying ${o.length} failed downloads...`);const i=Math.min(10,Math.ceil(o.length/5));let n=0;for(;o.length>0&&!h;){const s=o.splice(0,i).map(t=>(async()=>{const i=1e3*t.attempts+1e3*Math.random();await new Promise(e=>setTimeout(e,i));try{var s=await processInvoice(t.uuid);return n++,d++,updateProgressBar(r,e.length,"Retrying... Remaining: "+o.length),{success:!0,data:s}}catch(e){return n++,t.attempts++,t.attempts<a.retryLimit&&o.push(t),{success:!1}}})()),c=await Promise.all(s);for(const e of c)e.success&&e.data&&t.push(e.data);await new Promise(e=>setTimeout(e,500))}}updateProgressBar(e.length,e.length,`Download complete: ${t.length}/${e.length} successful (${Math.round(t.length/e.length*100)}%)`),h&&updateProgressBar(t.length,e.length,"Saving processed data..."),updateProgressBar(0,t.length,"Processing T4 tax data...");const s=new ExcelJS.Workbook,c=s.addWorksheet("نموذج 41");c.views=[{rightToLeft:!0,rtl:!0,activeCell:"A1",state:"frozen",ySplit:1}];const l=[{header:"مسلسل",key:"serial",width:10},{header:"رقم التسجيل الضريبي",key:"vatId",width:20},{header:"الرقم القومي",key:"nationalId",width:20},{header:"اسم الممول",key:"issuerName",width:30},{header:"العنوان",key:"address",width:30},{header:"اسم المامورية",key:"districtName",width:15},{header:"كود المامورية",key:"districtCode",width:15},{header:"تاريخ التعامل",key:"transactionDate",width:15},{header:"طبيعة التعامل",key:"transactionNature",width:15},{header:"القيمة الاجمالية للتعامل",key:"totalValue",width:20},{header:"نوع الخصم",key:"discountType",width:12},{header:"تاريخ التعامل للمبلغ المخصوم",key:"discountTransactionDate",width:25},{header:"القيمة الصافية للتعامل",key:"netValue",width:20},{header:"نسبة الخصم",key:"discountRate",width:12},{header:"المحصل لحساب الضريبة",key:"taxAmount",width:20}];c.columns=l;const u=[];let p=0;for(const e of t){if(!e)continue;var i=e.status;if("Valid"===e.status&&null!=e.cancelRequestDate&&null==e.declineCancelRequestDate&&(i="Cancellation Requested"),"Valid"===e.status&&null!=e.rejectRequestDate&&null==e.declineRejectRequestDate&&(i="Rejection Requested"),"Valid"!==i)continue;p++,updateProgressBar(p,t.length,`Processing Invoice ${p} of ${t.length}`);let o=!1;const a={};if(e.invoiceLines&&e.invoiceLines.length>0)for(const t of e.invoiceLines){const e=t.lineTaxableItems?.length>0?t.lineTaxableItems:t.taxableItems;if(e&&e.length>0)for(const i of e)if("T4"===i.taxType){if(!i.amount||0===i.amount)continue;o=!0;const e=`${i.rate||0}_${i.subType||"NONE"}`;a[e]||(a[e]={rate:i.rate||0,subType:i.subType||"",totalNetAmount:0,totalTaxAmount:0,lines:[]});const n=t.netTotal||t.salesTotal||0;a[e].totalNetAmount+=n,a[e].totalTaxAmount+=i.amount,a[e].lines.push(t)}}if(!o)continue;const r=e=>{if(!e)return"";const t=new Date(e);return`${(t.getDate()+"").padStart(2,"0")}.${(t.getMonth()+1+"").padStart(2,"0")}.${t.getFullYear()}`};let d=1,s=!1;e.documentType&&"C"===e.documentType.toUpperCase()&&(d=4,s=!0);const c=e.issuer?.type||0;let l="",h="";1===c?l=e.issuer?.id||"":h=e.issuer?.id||"";var n=e.issuer?.address??{};const f=`${n.buildingNumber||""} ${n.street||""} ${n.regionCity||""} ${n.governate||""}`.trim()||"مصر";for(const t in a){const o=a[t];let i="";const n=o.subType,c=o.rate;if(n){const e=n.match(/W0*(\d+)/);if(e){const t=parseInt(e[1]);1>t||t>10?"W014"===n?i="20":"W012"===n&&(i="19"):i=t.toString()}}else 10===c&&(i="11");const p={serial:"",vatId:h,nationalId:l,issuerName:e.issuer?.name||"",address:f,districtName:"",districtCode:"",transactionDate:r(e.dateTimeIssued),transactionNature:i,totalValue:Math.round(1e5*o.totalNetAmount)/1e5,discountType:d,discountTransactionDate:"",netValue:Math.round(1e5*o.totalNetAmount)/1e5,discountRate:c,taxAmount:Math.round(1e5*o.totalTaxAmount)/1e5};u.push({data:p,needsHighlight:s,emptyTransactionNature:!i})}}if(0===u.length)return updateProgressBar(0,0,"لا يوجد بند خصم وإضافة فى الفواتير المحددة"),setTimeout(()=>{finishDownload()},2e3),{success:!1,message:"No invoices found with T4 tax type having non-zero amounts"};updateProgressBar(t.length,t.length,"Writing Excel file..."),u.forEach(e=>{const t=c.addRow(e.data).getCell("discountTransactionDate");e.needsHighlight&&(t.fill={type:"pattern",pattern:"solid",fgColor:{argb:"FFFFFF00"}})}),c.addRow({}),c.addRow({serial:"يرجى العلم أن هذا التقرير لا يتضمن الدفعات المقدمة أو الخصومات اليدوية، حيث أن هذه البنود يكون على علم بها صاحب النشاط فقط."}).font={bold:!0,size:14,color:{argb:"FFFF0000"}};const f=c.getRow(1);f.font={bold:!0},f.alignment={horizontal:"center",vertical:"middle"},f.fill={type:"pattern",pattern:"solid",fgColor:{argb:"FFD9E1F2"}},f.height=20;for(let e=1;e<=u.length+1;e++)c.getRow(e).eachCell({includeEmpty:!0},e=>{e.border={top:{style:"thin"},left:{style:"thin"},bottom:{style:"thin"},right:{style:"thin"}}});c.autoFilter={from:"A1",to:{row:u.length+1,column:l.length}};const b=await s.xlsx.writeBuffer(),m=new Blob([b],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),A=(new Date).toISOString().slice(0,19).replace(/[:.]/g,"-"),g=`${ue?ue():"T4Report"}_T4-Tax-Report_${A}.xlsx`;saveAs(m,g);const w=h?`Process stopped - Saved ${u.length} T4 tax records`:`T4 report saved successfully - ${u.length} records processed`;return updateProgressBar(t.length,t.length,w),setTimeout(()=>{finishDownload()},2e3),{success:!0,rowCount:u.length,processedCount:t.length,totalCount:e.length,cancelled:h}}catch(e){return console.log("Error generating T4 tax report:",e),updateProgressBar(0,0,"Error: "+e.message),setTimeout(()=>{finishDownload()},3e3),{success:!1,error:e.message}}}function cancelT4Export(){h=!0,updateProgressBar(0,0,"Stopping T4 report process...")}function ee(){const e=JSON.parse(localStorage.getItem("USER_DATA"));var t="";return e&&e.profile&&e.profile.name&&(t=e.profile.name.toLowerCase()),t}function te(e){if(!e)return null;const t=new Date(e);var o=ae(t.getDate(),2),i=ae(t.getMonth()+1,2);return`${t.getFullYear()}-${i}-${o}`}function oe(e){var t=ae(e.getDate(),2),o=ae(e.getMonth()+1,2);return`${e.getFullYear()}-${o}-${t}`}function ie(e){if(!e)return null;const t=new Date(e),o=t.getUTCFullYear(),i=new Date(o,3,30),n=new Date(i);n.setDate(30-(i.getDay()+2)%7);const a=new Date(o,9,31),r=new Date(a);r.setDate(31-(a.getDay()+3)%7);const d=t>=n&&r>t?3:2;return new Date(t.getTime()+36e5*d)}function ne(e,t){return e?e+" - "+t:t}function ae(e,t){return(e=e.toString()).length<t?ae("0"+e,t):e}function re(e,t){return e.indexOf(e.filter(function(e){return e.key==t})[0])}function de(e){try{const t=Object.freeze(e);Object.defineProperty(window,"EXTENSION_SUBSCRIPTION",{value:t,writable:!1,configurable:!1}),W()}catch(e){}}function getTaxTypeDescription(e){return[{code:"T1",description:"ضريبة القيمة المضافة",width:18},{code:"T2",description:"ضريبة الجدول النسبية",width:18},{code:"T3",description:"ضريبة الجدول النوعية",width:18},{code:"T4",description:"الخصم تحت حساب الضريبة",width:22},{code:"T5",description:"ضريبة الدمغة النسبية",width:22},{code:"T13",description:"ضريبة الدمغة النسبية",width:22},{code:"T6",description:"ضريبة الدمغة قطعية بمقدار ثابت",width:30},{code:"T14",description:"ضريبة الدمغة قطعية بمقدار ثابت",width:30},{code:"T7",description:"ضريبة الملاهى",width:14},{code:"T15",description:"ضريبة الملاهى",width:14},{code:"T8",description:"رسم تنمية الموارد",width:18},{code:"T16",description:"رسم تنمية الموارد",width:18},{code:"T9",description:"رسم خدمة",width:12},{code:"T17",description:"رسم خدمة",width:12},{code:"T10",description:"رسم المحليات",width:14},{code:"T18",description:"رسم المحليات",width:14},{code:"T11",description:"رسم التامين الصحى",width:18},{code:"T19",description:"رسم التامين الصحى",width:18},{code:"T12",description:"رسوم أخرى",width:12},{code:"T20",description:"رسوم أخرى",width:12}].find(t=>t.code===e)||{code:e,description:"ضريبة غير معروفة",width:15}}function replaceUrlParam(e,t,o){null==o&&(o="");var i=RegExp("\\b("+t+"=).*?(&|#|$)");return 0>e.search(i)?(e=e.replace(/[?#]$/,""))+(e.indexOf("?")>0?"&":"?")+t+"="+o:e.replace(i,"$1"+o+"$2")}function se(e){const t=new TextEncoder,o=new WeakSet;return function e(i){if(o.has(i))throw new TypeError("Converting circular structure to JSON");if(i&&"function"==typeof i.toJSON&&(i=i.toJSON()),"object"==typeof i&&null!==i){o.add(i);const n=[],a=Array.isArray(i)?i:Object.entries(i);for(let o=0;o<a.length;o++){if(Array.isArray(i))n.push(e(a[o]));else{const[i,r]=a[o];n.push(t.encode(JSON.stringify(i)+":"),e(r))}o!==a.length-1&&n.push(t.encode(","))}const r=Array.isArray(i)?"[":"{",d=Array.isArray(i)?"]":"}";return new Blob([t.encode(r),...n,t.encode(d)])}return"function"==typeof i||void 0===i?t.encode("null"):t.encode(JSON.stringify(i))}(e)}function ce(e){var t="";for(var o in e){if(t+=e[o]instanceof Array?"":"<"+o+">",e[o]instanceof Array)for(var i in e[o])t+="<"+o+">",t+=ce(Object(e[o][i])),t+="</"+o+">";else"object"==typeof e[o]&&"document"!==o?t+=ce(Object(e[o])):t+=e[o];t+=e[o]instanceof Array?"":"</"+o+">"}return t.replace(/<\/?[0-9]{1,}>/g,"")}function le(e,t,o=(e,t)=>e===t){const i=[...e];return t.forEach(e=>i.some(t=>o(e,t))?null:i.push(e)),i}function ue(){return localStorage.getItem("RIN")}async function pe(){const e="chrome-extension://hgdnicmdpgmabbndlmmbpcdnaihelbkf/scripts";atob("************************************************"),window.__etaAuthor="hgdnicmdpgmabbndlmmbpcdnaihelbkf";const t={qrcode:e+"/qrcode.min.js",datatables:e+"/dataTables.min.js"},o={datatablesCSS:"chrome-extension://hgdnicmdpgmabbndlmmbpcdnaihelbkf/css/dataTables.min.css"};try{const e=await Promise.all(Object.values(t).map(e=>fetch(e).then(e=>e.text()))),i=await Promise.all(Object.values(o).map(e=>fetch(e).then(e=>e.text()))),n={},a={};return Object.keys(t).forEach((t,o)=>{n[t]=e[o]}),Object.keys(o).forEach((e,t)=>{a[e]=i[t]}),n.datatables=""+n.datatables,n.datatablesCSS=""+a.datatablesCSS,n}catch(e){return console.log("Error loading scripts:",e),null}}async function downloadExcelForReceipts(e,t){try{const y={top:{style:"thin"},left:{style:"thin"},bottom:{style:"thin"},right:{style:"thin"}},x={top:{style:"thin"},left:{style:"thin"},bottom:{style:"double"},right:{style:"thin"}};let v=window.EXTENSION_SUBSCRIPTION;if(void 0===v)return;if(!v.isSubscribed)return!1;var o=e.length;if(o>0){var i=$(".downloadAllBtn");$(i).addClass("disabled");var n=[],a=[];updateProgressBar(0,o,"Preparing Download Operation...");const v={batchSize:100,maxConcurrent:30,retryLimit:30,retryDelay:1e3,progressUpdateInterval:1};let k=0,D=0;for(let t=0;t<e.length&&!h;t+=v.batchSize){const i=e.slice(t,Math.min(t+v.batchSize,e.length));for(let e=0;e<i.length&&!h;e+=v.maxConcurrent){const t=i.slice(e,Math.min(e+v.maxConcurrent,i.length)).map(e=>{const t=e.receipt||e;return(async()=>{k++;try{const i=await processReceipt(t.uuid);return D++,k%v.progressUpdateInterval!==0&&k!==o||updateProgressBar(k,o,`Downloading ${k}/${o}`),{success:!0,data:{...e,docDetail:i.receipt}}}catch(t){return a.push({item:e,attempts:0}),k%v.progressUpdateInterval!==0&&k!==o||updateProgressBar(k,o,`Downloading ${k}/${o} (Success: ${D}, Queued: ${a.length})`),{success:!1}}})()}),r=await Promise.all(t);for(const e of r)e.success&&e.data&&n.push(e.data)}}if(a.length>0&&!h){updateProgressBar(o,o,`Retrying ${a.length} failed downloads...`);const e=Math.min(10,Math.ceil(a.length/5));for(;a.length>0&&!h;){const t=a.splice(0,e).map(e=>{const t=e.item.receipt||e.item;return(async()=>{const i=1e3*e.attempts+1e3*Math.random();await new Promise(e=>setTimeout(e,i));try{const i=await processReceipt(t.uuid);return D++,updateProgressBar(o,o,"Retrying... Remaining: "+a.length),{success:!0,data:{...e.item,docDetail:i.receipt}}}catch(t){return e.attempts++,e.attempts<v.retryLimit&&a.push(e),{success:!1}}})()}),i=await Promise.all(t);for(const e of i)e.success&&e.data&&n.push(e.data);await new Promise(e=>setTimeout(e,500))}}h?updateProgressBar(D,o,"Download Cancelled. Preparing Excel for downloaded data..."):updateProgressBar(o,o,`Download complete: ${n.length}/${o} successful (${Math.round(n.length/o*100)}%)`),updateProgressBar(o,o,"Preparing Excel Sheet...");const T=new ExcelJS.Workbook,C=T.addWorksheet("جميع الإيصالات");C.views=[{rightToLeft:!0,rtl:!0,activeCell:"A1",state:"frozen",ySplit:1}];const I=t?.columns||{};var r,d=[{header:"مسلسل",key:"index",width:10},{header:"تفاصيل",key:"details",width:10,style:{font:{name:"Comic Sans MS",family:4,size:12,underline:!0,bold:!0}}},{header:"نوع الإيصال",key:"type",width:12},{header:"الحالة",key:"status",width:10},{header:"تاريخ الإصدار",key:"dateTimeIssued",width:13},{header:"تاريخ الإستلام",key:"dateTimeReceived",width:13},{header:"عملة الإيصال",key:"currency",width:13},{header:"قيمة الإيصال",key:"netAmount",width:12},{header:"إجمالى الإيصال",key:"totalAmount",width:14},{header:"رقم الإيصال",key:"receiptNumber",width:23},{header:"الرقم الإلكترونى",key:"uuid",width:32},{header:"سيريال نقطة البيع",key:"deviceSerialNumber",width:17},{header:"إسم نقطة البيع",key:"submitterName",width:17},{header:"الرقم الضريبى للبائع",key:"issuerId",width:17},{header:"إسم البائع",key:"issuerName",width:32},{header:"عنوان البائع",key:"issuerAddress",width:32},{header:"الرقم الضريبى للمشترى",key:"receiverId",width:17},{header:"إسم المشترى",key:"receiverName",width:32},{header:"رقم المشترى",key:"receiverMobileNumber",width:32}].filter(e=>"index"===e.key||"details"===e.key||!1!==I[e.key]),s=[],c=[],l=[],u=[],p=1,f=1,b=[{header:"كود الصنف",key:"itemCode",width:14},{header:"إسم الكود",key:"itemCodeNameAr",width:14},{header:"الوصف",key:"description",width:25},{header:"الكمية",key:"quantity",width:10},{header:"السعر",key:"unitPrice",width:10},{header:"المبيعات",key:"netSale",width:10},{header:"القيمة",key:"netAmount",width:10},{header:"الإجمالى",key:"total",width:10,totalsRowFunction:"sum"}];if(t?.combineAll&&(b=[{header:"نوع الإيصال",key:"type",width:12},{header:"رقم الإيصال",key:"receiptNumber",width:23},{header:"تاريخ الإصدار",key:"dateTimeIssued",width:13},{header:"تاريخ الإستلام",key:"dateTimeReceived",width:13},{header:"كود الصنف",key:"itemCode",width:14},{header:"إسم الكود",key:"itemCodeNameAr",width:14},{header:"الوصف",key:"description",width:25},{header:"السعر",key:"unitPrice",width:10},{header:"الكمية",key:"quantity",width:10},{header:"المبيعات",key:"netSale",width:10},{header:"القيمة",key:"netAmount",width:10},{header:"الإجمالى",key:"total",width:10},{header:"سيريال نقطة البيع",key:"deviceSerialNumber",width:17},{header:"إسم نقطة البيع",key:"submitterName",width:17},{header:"الرقم الضريبى للبائع",key:"issuerId",width:17},{header:"إسم البائع",key:"issuerName",width:32},{header:"عنوان البائع",key:"issuerAddress",width:32},{header:"الرقم الضريبى للمشترى",key:"receiverId",width:17},{header:"إسم المشترى",key:"receiverName",width:32},{header:"رقم المشترى",key:"receiverMobileNumber",width:32}],r=T.addWorksheet("بيانات الإيصالات")),c=b.filter(e=>"index"===e.key||"details"===e.key||!1!==I[e.key]),t?.taxColumns){let e=new Set;n.forEach(t=>{t.docDetail&&t.docDetail.taxTotals&&t.docDetail.taxTotals.forEach(t=>{t.taxType&&e.add(t.taxType)})}),Array.from(e).sort((e,t)=>{const o=parseInt(e.substring(1));return parseInt(t.substring(1))-o}).forEach(e=>{const t=getTaxTypeDescription(e);d.splice(re(d,"netAmount")+1,0,{header:t.description,key:e,width:t.width})});let t=new Set;n.forEach(e=>{e.docDetail&&e.docDetail.itemData&&e.docDetail.itemData.forEach(e=>{e.taxableItems&&e.taxableItems.forEach(e=>{e.taxType&&t.add(e.taxType)})})}),Array.from(t).sort((e,t)=>parseInt(e.substring(1))-parseInt(t.substring(1))).forEach(e=>{if(-1===re(c,e)){const t=getTaxTypeDescription(e);c.splice(re(c,"total"),0,{header:t.description,key:e,width:t.width})}})}updateProgressBar(o,o,"Processing Receipt Details...");let E=0;for(const e of n){E++,updateProgressBar(E,n.length,`Processing ${E}/${n.length}`);var m=e.docDetail;if(void 0!==m){var A=m.seller.branchAddress??[],g={index:E,type:m.documentType.receiptTypeNameAr,status:m.status,dateTimeIssued:ie(m.dateTimeIssued),dateTimeReceived:ie(m.dateTimeReceived),currency:m.currency,receiptNumber:m.receiptNumber,netAmount:m.totalSales,totalAmount:m.totalAmount,uuid:m.uuid,deviceSerialNumber:e.posSerialNumber,submitterName:e.submitterName,issuerId:m.seller.sellerId,issuerName:m.seller.sellerName,issuerAddress:`${A.buildingNumber} ${A.street} ${A.regionCity} ${A.governate}`,receiverId:m.buyer.buyerId,receiverName:m.buyer.buyerName,receiverMobileNumber:m.buyer.mobileNumber};t?.taxColumns&&$.each(m.taxTotals,(e,t)=>{g[t.taxType]=t.amount}),t?.discountColumns&&(m.totalItemsDiscount&&m.totalItemsDiscount>0&&(-1==re(d,"itemsDiscount")&&d.splice(re(d,"totalAmount"),0,{header:"خصم الأصناف",key:"itemsDiscount",width:13}),g.itemsDiscount=m.totalItemsDiscount),m.totalCommercialDiscount&&m.totalCommercialDiscount>0&&(-1==re(d,"commercialDiscount")&&d.splice(re(d,"totalAmount"),0,{header:"خصم",key:"commercialDiscount",width:13}),g.commercialDiscount=m.totalCommercialDiscount));var w=E;t?.combineAll||(l=[],r=T.addWorksheet(""+w)),r.views=[{rightToLeft:!0,rtl:!0,activeCell:"A1",state:"frozen",ySplit:1}],f=p+1,u.push(f),$.each(m.itemData,(o,i)=>{p+=1;var n={status:m.status,type:m.documentType.receiptTypeNameAr,receiptNumber:m.receiptNumber,dateTimeIssued:ie(m.dateTimeIssued),dateTimeReceived:ie(m.dateTimeReceived),itemCode:i.itemCode,itemCodeNameAr:i.itemCodeNameAr,description:i.description,quantity:i.quantity,unitPrice:i.unitPrice,netSale:i.netSale,netAmount:i.netAmount,total:i.total,deviceSerialNumber:e.posSerialNumber,submitterName:e.submitterName,issuerId:m.seller.sellerId,issuerName:m.seller.sellerName,issuerAddress:`${A.buildingNumber} ${A.street} ${A.regionCity} ${A.governate}`,receiverId:m.buyer.buyerId,receiverName:m.buyer.buyerName,receiverMobileNumber:m.buyer.mobileNumber};t?.taxColumns&&$.each(i.taxableItems,(e,t)=>{n[t.taxType]=t.amount}),t?.discountColumns&&i.valueDifference&&i.valueDifference>0&&(-1==re(c,"valueDifference")&&c.splice(re(c,"total"),0,{header:"فرق قيمة لأغراض الضريبة",key:"valueDifference",width:20}),n.valueDifference=i.valueDifference),l.push(n),o!=m.itemData.length-1||t?.combineAll||(r.columns=c,l.forEach((e,t)=>{const o=r.addRow(e);o.eachCell({includeEmpty:!0},function(e){e.border=y}),"Valid"===m.status?o.fill={type:"pattern",pattern:"solid",fgColor:{argb:"FFC6EFCE"}}:"Invalid"!==m.status&&"Rejected"!==m.status&&"Cancelled"!==m.status||(o.fill={type:"pattern",pattern:"solid",fgColor:{argb:"FFFFC7CE"}}),t===l.length-1&&o.eachCell({includeEmpty:!0},function(e){e.border=x})}),r.addRow({itemCode:{text:"عودة",hyperlink:"#'جميع الإيصالات'!B"+(w+1)}}).getCell("A").font={name:"Calibri",size:14,underline:!0,bold:!0})}),g.details=t?.combineAll?{text:"عرض",hyperlink:"#'بيانات الإيصالات'!A"+f}:{text:"عرض",hyperlink:`#'${w}'!A1`},s.push(g)}}updateProgressBar(E,n.length,"Saving Excel File..."),C.columns=d,s.forEach(e=>{const t=C.addRow(e);t.eachCell({includeEmpty:!0},function(e){e.border=y}),"Valid"===e.status?t.fill={type:"pattern",pattern:"solid",fgColor:{argb:"FFC6EFCE"}}:"Invalid"!==e.status&&"Rejected"!==e.status&&"Cancelled"!==e.status||(t.fill={type:"pattern",pattern:"solid",fgColor:{argb:"FFFFC7CE"}})}),t?.combineAll&&(r.columns=c,l.forEach((e,t)=>{const o=r.addRow(e);o.eachCell({includeEmpty:!0},function(e){e.border=y}),"Valid"===e.status?o.fill={type:"pattern",pattern:"solid",fgColor:{argb:"FFC6EFCE"}}:"Rejected"!==e.status&&"Cancelled"!==e.status||(o.fill={type:"pattern",pattern:"solid",fgColor:{argb:"FFFFC7CE"}});const i=l[t+1];i&&i.receiptNumber!==e.receiptNumber&&o.eachCell({includeEmpty:!0},function(e){e.border={...e.border,bottom:{style:"medium"}}}),t===l.length-1&&o.eachCell({includeEmpty:!0},function(e){e.border=x})}),$.each(u,(e,t)=>{r.getCell("A"+t).font={name:"Calibri",size:11,underline:!0,bold:!0}})),C.getCell("B1").font={name:"Calibri",size:11,underline:!1,bold:!1},C.autoFilter={from:"A1",to:{row:s.length+1,column:d.length}},r&&(r.autoFilter={from:"A1",to:{row:l.length+1,column:c.length}}),T.xlsx.writeBuffer().then(function(e){var t=new Blob([e],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"});const o=ue();saveAs(t,`eReceipts_${o}.xlsx`),updateProgressBar(n.length,n.length,"File Saved Successfully"),setTimeout(()=>{finishDownload()},1500)})}}catch(e){console.log(e),updateProgressBar(0,0,"Error: "+e.message),setTimeout(()=>{finishDownload()},1500)}}q(),document.addEventListener("click",e=>{const t=e.target;(t.closest('[role="tab"]')||t.closest(".ms-Pivot-link")||t.closest('[data-automationid*="Pivot"]')||t.closest(".ms-Nav-link"))&&setTimeout(debouncedInit,300)},!0);