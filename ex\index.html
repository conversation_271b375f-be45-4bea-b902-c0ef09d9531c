<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Egypt ETA - Invoice Portal (Test Environment)</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="styles.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            direction: rtl;
        }
        .navbar {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .navbar-brand {
            color: white !important;
            font-weight: bold;
        }
        .main-container {
            margin-top: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        .invoice-card {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            margin-bottom: 15px;
            transition: all 0.3s ease;
        }
        .invoice-card:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        .invoice-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 15px;
            border-radius: 8px 8px 0 0;
        }
        .invoice-body {
            padding: 15px;
        }
        .status-badge {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
        }
        .status-valid {
            background-color: #d4edda;
            color: #155724;
        }
        .status-submitted {
            background-color: #cce5ff;
            color: #004085;
        }
        .btn-download {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            border: none;
            color: white;
            padding: 8px 16px;
            border-radius: 5px;
            transition: all 0.3s ease;
        }
        .btn-download:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
        }
        .search-container {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .test-notice {
            background: linear-gradient(135deg, #ffc107 0%, #ffca2c 100%);
            color: #212529;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
            font-weight: bold;
            border: 2px solid #ffc107;
        }
        #tooltip1 {
            background: #007bff;
            color: white;
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 14px;
        }
        .pagination-container {
            display: flex;
            justify-content: center;
            margin-top: 20px;
        }
        .loading-spinner {
            display: none;
            text-align: center;
            padding: 20px;
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg">
        <div class="container">
            <a class="navbar-brand" href="#">
                <img src="data:image/png;base64,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" width="30" height="30" alt="ETA Logo">
                نظام الفواتير الإلكترونية - بيئة الاختبار
            </a>
            <div class="navbar-nav ms-auto">
                <a href="login.html" class="nav-link">تسجيل الدخول</a>
                <a href="print.html" class="nav-link">عرض فاتورة</a>
                <span id="tooltip1" class="nav-link"><EMAIL></span>
            </div>
        </div>
    </nav>

    <div class="container">
        <!-- Test Environment Notice -->
        <div class="test-notice">
            <i class="fas fa-exclamation-triangle"></i>
            هذه بيئة اختبار لإضافة Egypt ETA PDF Tool - البيانات وهمية لأغراض الاختبار فقط
        </div>

        <!-- Search Container -->
        <div class="search-container">
            <h4 class="mb-3">البحث في الفواتير</h4>
            <div class="row">
                <div class="col-md-4">
                    <label class="form-label">من تاريخ</label>
                    <input type="date" class="form-control" id="dateFrom" value="2024-01-01">
                </div>
                <div class="col-md-4">
                    <label class="form-label">إلى تاريخ</label>
                    <input type="date" class="form-control" id="dateTo" value="2024-12-31">
                </div>
                <div class="col-md-4">
                    <label class="form-label">حالة الفاتورة</label>
                    <select class="form-control" id="statusFilter">
                        <option value="">جميع الحالات</option>
                        <option value="Valid">صالحة</option>
                        <option value="Submitted">مرسلة</option>
                    </select>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-12">
                    <button class="btn btn-primary" onclick="searchInvoices()">
                        <i class="fas fa-search"></i> بحث
                    </button>
                    <button class="btn btn-secondary ms-2" onclick="loadSampleData()">
                        <i class="fas fa-refresh"></i> تحميل بيانات تجريبية
                    </button>
                </div>
            </div>
        </div>

        <!-- Loading Spinner -->
        <div class="loading-spinner" id="loadingSpinner">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <p class="mt-2">جاري تحميل الفواتير...</p>
        </div>

        <!-- Main Container -->
        <div class="main-container">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h3>قائمة الفواتير الإلكترونية</h3>
                <div>
                    <span class="badge bg-info">إجمالي الفواتير: <span id="totalCount">0</span></span>
                </div>
            </div>

            <!-- Invoices Container -->
            <div id="invoicesContainer">
                <!-- Invoices will be loaded here -->
            </div>

            <!-- Pagination -->
            <div class="pagination-container">
                <nav aria-label="صفحات الفواتير">
                    <ul class="pagination" id="paginationContainer">
                        <!-- Pagination will be generated here -->
                    </ul>
                </nav>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://kit.fontawesome.com/a076d05399.js" crossorigin="anonymous"></script>
    <script src="config.js"></script>
    <script src="mock-api.js"></script>
    <script src="app.js"></script>
</body>
</html>
