/*!
 * Egypt ETA PDF Tool, Chrome Extension Build.
 * Generated: 2025-08-20T18:53:13.677Z
 * Build Version: 31
 */
function t(t){const e=document.createElement("script");e.type="text/javascript",e.src=chrome.runtime.getURL(t),e.defer="defer",(document.body||document.head||document.documentElement).appendChild(e)}function e(t){const e=document.createElement("link");e.rel="stylesheet",e.type="text/css",e.href=chrome.runtime.getURL(t),(document.body||document.head||document.documentElement).appendChild(e)}if(!window.location.href.startsWith("https://invoicing.eta.gov.eg/print")){e("css/bootstrap.min.css"),e("css/style.min.css");const t=JSON.parse(localStorage.getItem("USER_DATA"));if(t&&t.profile&&t.profile.name)s(t.profile.name.toLowerCase());else{let t;const e=setInterval(()=>{t=document.getElementById("tooltip1"),t&&(clearInterval(e),s(t.innerText.toLowerCase()))},100)}}function s(e){chrome.runtime.sendMessage({action:"setActiveUser",email:e}),t("scripts/extensionBase.min.js"),t("scripts/jszip.min.js"),t("scripts/polyfill.js"),setTimeout(()=>{t("scripts/exceljs/exceljs.min.js")},2e3)}window.addEventListener("RequestExtensionData",()=>{chrome.runtime.sendMessage({action:"checkStatusAndDownload"},t=>{if(t){const e=new CustomEvent("ReceiveExtensionData",{detail:t});window.dispatchEvent(e)}})});