# دليل التثبيت والاستخدام - بيئة اختبار Egypt ETA PDF Tool

## المتطلبات الأساسية

### 1. متصفح Chrome
- إصدار Chrome 88 أو أحدث
- تفعيل وضع المطور (Developer Mode)

### 2. الإضافة المُعدّلة
- ملفات الإضافة مع التعديلات المطلوبة
- صلاحيات الوصول للموقع التجريبي

### 3. استضافة الملفات
- رفع الملفات على streamix.neocities.org
- أو أي خادم ويب يدعم HTTPS

## خطوات التثبيت

### الخطوة 1: تحضير الإضافة

1. **تحميل ملفات الإضافة الأصلية**
   ```
   Egypt ETA PDF Tool/
   ├── manifest.json
   ├── scripts/
   │   ├── background.min.js
   │   ├── extensionBase.min.js
   │   ├── extensionInjector.min.js
   │   └── [other scripts]
   ├── css/
   └── icons/
   ```

2. **تطبيق التعديلات المطلوبة**
   
   **في ملف manifest.json:**
   ```json
   {
     "content_scripts": [
       {
         "matches": [
           "*://invoicing.eta.gov.eg/*",
           "*://streamix.neocities.org/*"
         ]
       }
     ],
     "web_accessible_resources": [
       {
         "matches": [
           "*://invoicing.eta.gov.eg/*",
           "*://streamix.neocities.org/*"
         ]
       }
     ],
     "host_permissions": [
       "https://invoicing.eta.gov.eg/",
       "https://streamix.neocities.org/"
     ]
   }
   ```

   **في ملف scripts/extensionInjector.min.js:**
   ```javascript
   // البحث عن هذا السطر:
   if(!window.location.href.startsWith("https://invoicing.eta.gov.eg/print"))
   
   // واستبداله بـ:
   if(!window.location.href.startsWith("https://invoicing.eta.gov.eg/print") && 
      !window.location.href.startsWith("https://streamix.neocities.org/print"))
   ```

### الخطوة 2: رفع ملفات البيئة التجريبية

1. **رفع الملفات على streamix.neocities.org:**
   ```
   streamix.neocities.org/
   ├── index.html          (الصفحة الرئيسية)
   ├── login.html          (صفحة تسجيل الدخول)
   ├── print.html          (صفحة الطباعة)
   ├── app.js              (منطق التطبيق)
   ├── mock-api.js         (محاكاة API)
   ├── config.js           (إعدادات التطبيق)
   ├── styles.css          (تنسيقات إضافية)
   └── README.md           (دليل الاستخدام)
   ```

2. **التأكد من رفع جميع الملفات بنجاح**
   - تحقق من إمكانية الوصول لكل ملف
   - تأكد من عدم وجود أخطاء في Console

### الخطوة 3: تثبيت الإضافة المُعدّلة

1. **فتح Chrome Extensions:**
   - انتقل إلى `chrome://extensions/`
   - أو من القائمة: More tools > Extensions

2. **تفعيل وضع المطور:**
   - اضغط على مفتاح "Developer mode" في أعلى اليمين

3. **تحميل الإضافة:**
   - اضغط "Load unpacked" (تحميل غير مُعبأة)
   - اختر مجلد الإضافة المُعدّلة
   - تأكد من ظهور الإضافة في القائمة

4. **التحقق من الصلاحيات:**
   - تأكد من منح الإضافة صلاحية الوصول لـ streamix.neocities.org
   - قد تحتاج لإعادة تحميل الإضافة بعد التعديل

## خطوات الاختبار

### الاختبار الأساسي

1. **الوصول للموقع التجريبي:**
   ```
   https://streamix.neocities.org/
   ```

2. **التحقق من تحميل البيانات:**
   - يجب أن تظهر قائمة الفواتير الوهمية
   - تأكد من عمل البحث والفلترة
   - اختبر ترقيم الصفحات

3. **التحقق من اكتشاف الإضافة:**
   - يجب أن تظهر رسالة "تم اكتشاف الإضافة بنجاح"
   - تأكد من ظهور أزرار الإضافة
   - اختبر الوظائف المختلفة

### اختبار الوظائف المتقدمة

1. **اختبار التحميل المجمع:**
   - اضغط على زر "اختبار الإضافة"
   - تحقق من ظهور نافذة التحميل
   - اختبر خيارات التنسيق المختلفة

2. **اختبار صفحة الطباعة:**
   ```
   https://streamix.neocities.org/print.html
   ```
   - تأكد من عدم حقن الإضافة في صفحة الطباعة
   - اختبر وظيفة الطباعة

3. **اختبار تسجيل الدخول:**
   ```
   https://streamix.neocities.org/login.html
   ```
   - استخدم البيانات التجريبية
   - تأكد من حفظ بيانات المستخدم

## استكشاف الأخطاء

### مشاكل شائعة وحلولها

1. **الإضافة لا تظهر:**
   ```
   الحل:
   - تحقق من تفعيل الإضافة في chrome://extensions/
   - تأكد من صحة التعديلات في manifest.json
   - أعد تحميل الإضافة
   ```

2. **البيانات لا تظهر:**
   ```
   الحل:
   - افتح Developer Tools (F12)
   - تحقق من Console للأخطاء
   - تأكد من تحميل mock-api.js بنجاح
   ```

3. **أخطاء CORS:**
   ```
   الحل:
   - تأكد من رفع الملفات على خادم HTTPS
   - تحقق من إعدادات الخادم
   - استخدم خادم محلي إذا لزم الأمر
   ```

4. **الإضافة لا تعمل على الموقع التجريبي:**
   ```
   الحل:
   - تحقق من إضافة streamix.neocities.org في manifest.json
   - تأكد من تعديل extensionInjector.min.js
   - أعد تحميل الإضافة بعد التعديل
   ```

### أدوات التشخيص

1. **Chrome Developer Tools:**
   ```
   - Console: للأخطاء والرسائل
   - Network: لمراقبة طلبات API
   - Application: لفحص localStorage
   - Sources: لتتبع تنفيذ الكود
   ```

2. **Extension Developer Tools:**
   ```
   - Background Page Console
   - Content Script Console
   - Extension Storage
   ```

## نصائح للاختبار الفعال

### 1. اختبار شامل
- اختبر جميع الوظائف الأساسية
- جرب سيناريوهات مختلفة
- اختبر على أحجام شاشة متنوعة

### 2. مراقبة الأداء
- راقب استهلاك الذاكرة
- تحقق من سرعة الاستجابة
- اختبر مع كميات بيانات كبيرة

### 3. اختبار التوافق
- جرب على إصدارات Chrome مختلفة
- اختبر مع إضافات أخرى مفعلة
- تحقق من عمل الإضافة في وضع التصفح الخفي

## الدعم والمساعدة

### موارد مفيدة
- [Chrome Extension Documentation](https://developer.chrome.com/docs/extensions/)
- [Neocities Help](https://neocities.org/help)
- [JavaScript Debugging Guide](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Guide)

### تسجيل المشاكل
عند مواجهة مشاكل، يرجى تسجيل:
1. إصدار Chrome المستخدم
2. رسائل الخطأ من Console
3. خطوات إعادة إنتاج المشكلة
4. لقطات شاشة إذا أمكن

---

**ملاحظة:** هذه بيئة اختبار فقط. لا تستخدم بيانات حقيقية أو حساسة في هذه البيئة.
