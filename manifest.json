{"update_url": "https://clients2.google.com/service/update2/crx", "manifest_version": 3, "name": "Egypt ETA PDF Tool", "description": "Egyptian eTax - eInvoice PDF Tool, save all eInvoices with one click and export an excel sheet with all the needed data", "version": "34", "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEApKYZq9/dDKmKp51t3u1ErJ2oIXR9GsKo5Oz+NJHtoepTijBKbLTq1pifEL1S/r+pIjgaLfRoShY2WqJioe8IV3j5V97rs8GnhpILDNPT6IFsUnvE5lYc8nQHZpZ322fQQVujZKra6sV5g64zknPuUuz6MmPJLEV29uCLPVnxwUze9O27ZiVb/79bVAcZ/Eudp6EJmuHuNJ+/b65938MaljaW7MgAqB4g40kItEApDEMb5KEQ3AbVVwg7OTNSCrSx68uxlIsBWT1gljOeTsXG1K07gss46w89aowhsazzNiygGtsmDxy9DyaH1I5X/kWmwkzf3EZPzbdAocKmwZIozwIDAQAB", "content_scripts": [{"matches": ["*://invoicing.eta.gov.eg/*", "*://streamix.neocities.org/*"], "js": ["scripts/extensionInjector.min.js"], "run_at": "document_start"}], "content_security_policy": {"extension_pages": "script-src 'self'; object-src 'self';"}, "web_accessible_resources": [{"resources": ["scripts/extensionBase.min.js", "scripts/jszip.min.js", "scripts/polyfill.js", "scripts/exceljs/exceljs.min.js", "scripts/qrcode.min.js", "scripts/dataTables.min.js*", "css/style.min.css", "css/dataTables.min.css", "css/bootstrap.min.css"], "matches": ["*://invoicing.eta.gov.eg/*", "*://streamix.neocities.org/*"]}], "host_permissions": ["https://invoicing.eta.gov.eg/", "https://streamix.neocities.org/"], "background": {"service_worker": "scripts/background.min.js"}, "permissions": ["storage"], "icons": {"16": "icons/logo16.png", "24": "icons/logo24.png", "48": "icons/logo48.png", "128": "icons/logo128.png"}}