/*!
 * Egypt ETA PDF Tool, Chrome Extension Build.
 * Background Script
 * Chrome Extension Service Worker
 * © 2023-2025 <PERSON>. All Rights Reserved.
 * This code is protected by copyright law.
 * Unauthorized reproduction or distribution may result in legal action.
 * Licensed for use only with valid subscription from extension.abouelela.net
 * Generated: 2025-08-20T18:53:13.678Z
 */
let t=null;const e="5e2807cc24604b6c86ece63b9e99b52f";async function checkSubscriptionForCurrentUser(){try{let e=((await chrome.storage.local.get(["authTokens"])).authTokens||{})[t];if(e){const t=await n(e);if(401!==t.status)return await t.json()}const o=await r(t);if(!o)return{isSubscribed:!1,errorOccurred:!0,error:"Failed to get new token"};const a=await n(o);return await a.json()}catch(t){return console.log("Background: Failed to get token: ",t),null}}function n(t){try{return fetch("https://extension.abouelela.net/api/subscription/status",{method:"GET",headers:{"Authorization":"Bearer "+t,"Ocp-Apim-Subscription-Key":e}})}catch(t){return console.log("Background: Failed to get token: ",t),null}}async function r(t){try{const n=await fetch("https://extension.abouelela.net/api/subscription/authenticate",{method:"POST",headers:{"Content-Type":"application/json","Ocp-Apim-Subscription-Key":e},body:JSON.stringify({email:t})});if(!n.ok)return null;const r=(await n.json()).token,o=(await chrome.storage.local.get(["authTokens"])).authTokens||{};return o[t]=r,await chrome.storage.local.set({authTokens:o}),r}catch(e){return console.log("Background: Failed to get/store new token for "+t+":",e),null}}chrome.runtime.onMessage.addListener((e,n,r)=>{try{if("setActiveUser"===e.action)return t=e.email,!0;if("checkStatusAndDownload"===e.action)return t?(checkSubscriptionForCurrentUser().then(t=>{r(t)}),!0):(console.log("Background: Cannot check status, active user email is unknown."),r({isSubscribed:!1,errorOccurred:!0,error:"No active user"}),!0)}catch(t){return console.log("Background: Cannot check status: ",t),!0}});