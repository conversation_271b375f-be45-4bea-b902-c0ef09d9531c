﻿# Chrome Extension - Egypt ETA PDF Tool

## Overview
This Chrome extension provides enhanced functionality for processing and exporting e-invoices and receipts from the ETA portal. It offers bulk download capabilities in multiple formats (Excel, PDF, JSON/XML) with customizable options.

## Description
Egyptian eTax - eInvoice PDF Tool, save all eInvoices with one click and export an excel sheet with all the needed data

## Official Website
[https://abouelela.net](https://abouelela.net)

## Build Process

### Technologies Used
- **JavaScript ES6+** - Core functionality
- **Terser** - Industry-standard JavaScript minification
- **Node.js** - Build tooling
- **No obfuscation** - Only standard minification for performance

### Minification Details
- **Tool**: Terser with conservative settings
- **Purpose**: File size optimization and performance improvement
- **Method**: Standard variable renaming and whitespace removal
- **Preservation**: All Chrome APIs and third-party library names are preserved
- **No encryption**: All strings and logic remain visible
- **No obfuscation**: Code structure remains clear for review by Google Team

## Third-Party Libraries
All libraries are used in their original, unmodified form:
- **ExcelJS** (exceljs.min.js) - For Excel file generation
- **JSZip** (jszip.min.js) - For creating ZIP archives
- **Polyfill.js** - For browser compatibility
- **Bootstrap** (bootstrap.min.css) - For UI styling

## Code Structure
```
extension/
├── src/
│   ├── scripts/
│   │   ├── background.js         - Service worker
│   │   ├── extensionBase.js      - Core functionality
│   │   ├── extensionInjector.js  - Content script injector
│   │   └── [third-party libs]    - Unchanged libraries
│   └── css/
│       └── style.css             - Custom styles
├── dist/                         - Production build
├── manifest.json                 - Extension manifest
└── build.js                      - Build script
```

## Features
- Bulk download of invoices and receipts
- Multiple export formats (Excel, PDF, JSON, XML)
- Customizable file naming options
- Progress tracking for large downloads
- Automatic retry for failed downloads
- Memory-efficient batch processing

## Compliance
This extension follows all Chrome Web Store policies:
- ✅ Standard minification only (no obfuscation)
- ✅ Clear code purpose and functionality
- ✅ All third-party libraries properly attributed
- ✅ No hidden or encrypted functionality
- ✅ Transparent build process

## Testing
The extension has been thoroughly tested:
- All features work correctly with minified code
- Performance optimized for large data sets
- Compatible with the latest Chrome versions
- Handles errors gracefully

## Support
For any questions about the code or build process, please contact me by mail: <EMAIL>.

---
*This extension uses standard web development practices and complies with all Chrome Web Store guidelines.*